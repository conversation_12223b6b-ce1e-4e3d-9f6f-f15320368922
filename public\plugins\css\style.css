
/*
1. <PERSON>
2. <PERSON><PERSON>
3. <PERSON><PERSON><PERSON> Custom
4. <PERSON><PERSON><PERSON>
5. <PERSON><PERSON>
6. <PERSON>s
7. Dash<PERSON>
8. <PERSON><PERSON> Only
9. <PERSON><PERSON>
10. Cards
11. Pagination
12. Progressbars
13. Popovers & Tooltips
14. <PERSON> Alert
15. Background
16. Alertify
17. Charts
18. Tables
19. Rage Slider
20. Menu
21. Form Elements
22. Form Advanced
23. Form Validation
24. Form Uploads
25. Form Editor
26. Summernote
27. Calendar
28. Widgets
29. Maps
30. Account pages 
31. Responsive 
*/@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500&display=swap');
/* ==============
// Author Name: <PERSON><PERSON>. 
// for any PHP, Codeignitor, <PERSON><PERSON> Python work contact <NAME_EMAIL>  
// Visit website : www.mayurik.com
===================*/
body {
  background: #eff3f6;
  font-family: "Poppins", sans-serif;
  margin: 0;
  font-size: 14px;
}


html {
  overflow-x: hidden;
  position: relative;
  min-height: 100%;
  background: #eff3f6;
}

h1, h2, h3, h4, h5, h6 {
  margin: 10px 0;
  font-weight: 600;
}

h1 {
  line-height: 43px;
}

h2 {
  line-height: 35px;
}

h3 {
  line-height: 30px;
}

h3 small {
  color: #343a40;
}

h4 {
  line-height: 22px;
}

h4 small {
  color: #343a40;
}

h5 small {
  color: #343a40;
}

b {
  font-weight: 500;
}

* {
  outline: none !important;
}

a {
  color: #6c757d;
}

a:hover {
  outline: 0;
  text-decoration: none;
  color: #6c757d;
}

a:active {
  outline: 0;
  text-decoration: none;
  color: #6c757d;
}

a:focus {
  outline: 0;
  text-decoration: none;
  color: #6c757d;
}

code {
  color: #44a2d2;
}

.tab-btn {
  display: none;
}

.container-alt {
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}

.footer {
  background-color: #ffffff;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  bottom: 0;
  color: #2d3b48;
  text-align: center;
  padding: 20px 30px;
  position: absolute;
  right: 0;
  left: 240px;
}

#wrapper {
  height: 100%;
  overflow: hidden;
  width: 100%;
}

.page {
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
}

.header-title {
  font-size: 16px;
}

/* Social */
.social-links li a {
  background: #fcfdfd;
  border-radius: 50%;
  color: #939ba2;
  display: inline-block;
  height: 30px;
  line-height: 30px;
  text-align: center;
  width: 30px;
}

.card-thumbnail[class*=card-inner] {
  display: block;
  margin-top: -66px;
  margin-bottom: 15px;
}

.card-inner {
  display: inline-block;
  height: 80px;
  line-height: 80px;
  width: 80px;
}

/* ==============
  Loader
===================*/
#preloader {
display: block;
position: absolute;
top: 0;
left: 0;
z-index: 100;
width: 100%;
height: 100vh;
background-color: #fff;
background-image: url("../images/loader.gif");
background-repeat: no-repeat;
background-position: center;
background-size: 20%; /* Adjust the percentage as needed */
}

#status {
  width: 40px;
  height: 40px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -20px 0 0 -20px;
}


@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  50% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(1080deg);
            transform: rotate(1080deg);
  }
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  50% {
    -webkit-transform: rotate(360deg);
  }
  100% {
    -webkit-transform: rotate(1080deg);
  }
}

@keyframes pulse {
  0% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  13% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  15% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  28% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  30% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  43% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  45% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  70% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  74% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  100% {
    background-color: rgba(68, 162, 210, 0.9);
  }
}

@-webkit-keyframes pulse {
  0% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  13% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  15% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  28% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  30% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  43% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  45% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  70% {
    background-color: rgba(68, 162, 210, 0.9);
  }
  74% {
    background-color: rgba(68, 162, 210, 0.2);
  }
  100% {
    background-color: rgba(68, 162, 210, 0.9);
  }
}

@keyframes borderPulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 white, 0 0 0 1px rgba(68, 162, 210, 0.8);
            box-shadow: 0 0 0 0 white, 0 0 0 1px rgba(68, 162, 210, 0.8);
  }
  40% {
    -webkit-box-shadow: 0 0 0 1px white, 0 0 0 2px rgba(68, 162, 210, 0.8);
            box-shadow: 0 0 0 1px white, 0 0 0 2px rgba(68, 162, 210, 0.8);
  }
  80% {
    -webkit-box-shadow: 0 0 0 3px #ffffff, 0 0 1px 3px rgba(68, 162, 210, 0.8);
            box-shadow: 0 0 0 3px #ffffff, 0 0 1px 3px rgba(68, 162, 210, 0.8);
  }
}

@-webkit-keyframes borderPulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 white, 0 0 0 1px rgba(68, 162, 210, 0.8);
            box-shadow: 0 0 0 0 white, 0 0 0 1px rgba(68, 162, 210, 0.8);
  }
  40% {
    -webkit-box-shadow: 0 0 0 1px white, 0 0 0 2px rgba(68, 162, 210, 0.8);
            box-shadow: 0 0 0 1px white, 0 0 0 2px rgba(68, 162, 210, 0.8);
  }
  80% {
    -webkit-box-shadow: 0 0 0 3px #ffffff, 0 0 1px 3px rgba(68, 162, 210, 0.8);
            box-shadow: 0 0 0 3px #ffffff, 0 0 1px 3px rgba(68, 162, 210, 0.8);
  }
}

@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

/* ==============
  Bootstrap-custom
===================*/
.row {
  margin-right: -10px;
  margin-left: -10px;
}

.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9,
.col-auto, .col-lg, .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5,
.col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-md-10, .col-md-11, .col-md-12,
.col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm,
.col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7,
.col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3,
.col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto {
  padding-right: 10px;
  padding-left: 10px;
}

.breadcrumb {
  background-color: transparent;
  margin-bottom: 8px;
  margin-top: 5px;
  float: inline-end;
}

.dropdown-menu {
  padding: 4px 0;
  font-size: 15px;
  -webkit-box-shadow: 0 2px 30px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 30px rgba(0, 0, 0, 0.08);
  border-color: #eff3f6;
}

.dropdown-item.active, .dropdown-item:active {
  color: #2d3b48;
  text-decoration: none;
  background-color: #eff3f6;
}

.dropdown-item {
  padding: .55rem 1.5rem;
}

.bg-primary {
  background-color: #067bbc !important;
}

.bg-success {
  background-color: #29b348 !important;
}

.bg-info {
  background-color: #44a2d2 !important;
}

.bg-warning {
  background-color: #f5b225 !important;
}

.bg-danger {
  background-color: #ec536c !important;
}

.bg-muted {
  background-color: #a1a7cc !important;
}

.bg-white {
  background-color: #ffffff !important;
}

.text-white {
  color: #ffffff !important;
}

.text-danger {
  color: #ec536c !important;
}

.text-muted {
  color: #a1a7cc !important;
}

.text-primary {
  color: #067bbc !important;
}

.text-warning {
  color: #f5b225 !important;
}

.text-success {
  color: #29b348 !important;
}

.text-info {
  color: #44a2d2 !important;
}

.text-dark {
  color: #2d3b48 !important;
}

.text-pink {
  color: #ec408f !important;
}

.text-purple {
  color: #7043c1 !important;
}

.text-light {
  color: #eff3f6 !important;
}

.badge {
  font-weight: 500;
}

.badge-default {
  background-color: #eff3f6;
  color: #2d3b48;
}

.badge-primary {
  background-color: #067bbc;
}

.badge-success {
  background-color: #29b348;
}

.badge-info {
  background-color: #44a2d2;
}

.badge-warning {
  background-color: #f5b225;
}

.badge-danger {
  background-color: #ec536c;
}

.badge-dark {
  background-color: #2d3b48;
}

/* Navs & Tabs */
.nav-pills .nav-item.show .nav-link, .nav-pills .nav-link.active {
  background-color: #067bbc;
}

.nav-pills > .active > a > .badge {
  color: #067bbc;
}

/* List Group */
.list-group-item.active {
  background-color: #ced4da;
  border-color: #ced4da;
  color: #343a40;
  z-index: 2;
}

.list-group-item.active:hover {
  background-color: #ced4da;
  border-color: #ced4da;
  color: #343a40;
  z-index: 2;
}

.list-group-item.active:hover .list-group-item-text {
  color: #067bbc;
}

.list-group-item.active:focus {
  background-color: #ced4da;
  border-color: #ced4da;
  color: #343a40;
  z-index: 2;
}

.list-group-item.active:focus .list-group-item-text {
  color: #067bbc;
}

.list-group-item.active .list-group-item-text {
  color: #067bbc;
}

.list-group-item {
  border-radius: 0;
  padding: 12px 20px;
  border: 1px solid fade(#2d3b48, 6%);
}

.list-group-item:first-child {
  border-radius: 0;
  padding: 12px 20px;
}

.list-group-item:last-child {
  border-radius: 0;
  padding: 12px 20px;
}

.list-group-item-heading {
  font-weight: 300;
}

.list-group-item.active > .badge {
  color: #067bbc;
}

.popover-header {
  margin-top: 0;
}

.blockquote {
  font-size: 1.05rem;
}

.modal-title {
  margin-top: 0;
}

.dropdown a:focus {
  color: #ffffff;
}

.grid-structure .grid-container {
  background-color: #f8f9fa;
  margin-bottom: 10px;
  padding: 10px 20px;
}

/* =============
   Alerts
============= */
.alert {
  position: relative;
  border: 0;
}

.alert .alert-link {
  font-weight: 600;
}

.alert-success {
  color: #29b348;
  background-color: #e3f8e7;
}

.alert-success .alert-link {
  color: #1f8a37;
}

.alert-success hr {
  border-top-color: #1f8a37;
}

.alert-info {
  color: #44a2d2;
  background-color: #d4eaf5;
}

.alert-info .alert-link {
  color: #2c88b7;
}

.alert-info hr {
  border-top-color: #2c88b7;
}

.alert-warning {
  color: #f5b225;
  background-color: #fef3de;
}

.alert-warning .alert-link {
  color: #dd990a;
}

.alert-warning hr {
  border-top-color: #dd990a;
}

.alert-danger {
  color: #ec536c;
  background-color: #fbdde2;
}

.alert-danger .alert-link {
  color: #e72545;
}

.alert-danger hr {
  border-top-color: #e72545;
}

/* ==============
  Helper Classes
===================*/
.p-0 {
  padding: 0;
}

.p-t-10 {
  padding-top: 10px;
}

.p-b-10 {
  padding-bottom: 10px;
}

.p-r-30 {
  padding-right: 30px;
}

.m-0 {
  margin: 0;
}

.m-r-5 {
  margin-right: 5px;
}

.m-r-10 {
  margin-right: 10px;
}

.m-r-15 {
  margin-right: 15px;
}

.m-l-10 {
  margin-left: 10px;
}

.m-l-15 {
  margin-left: 15px;
}

.m-t-5 {
  margin-top: 5px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-t-15 {
  margin-top: 15px;
}

.m-t-20 {
  margin-top: 20px;
}

.m-t-30 {
  margin-top: 30px;
}

.m-t-40 {
  margin-top: 40px;
}

.m-b-5 {
  margin-bottom: 5px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.m-b-15 {
  margin-bottom: 15px;
}

.m-b-20 {
  margin-bottom: 20px;
}

.m-b-30 {
  margin-bottom: 30px;
}

.w-xs {
  min-width: 80px;
}

.w-sm {
  min-width: 95px;
}

.w-md {
  min-width: 110px;
}

.w-lg {
  min-width: 140px;
}

.m-h-50 {
  min-height: 50px;
}

.l-h-34 {
  line-height: 34px;
}

.font-12 {
  font-size: 12px;
}

.font-14 {
  font-size: 14px;
}

.font-16 {
  font-size: 16px;
}

.font-18 {
  font-size: 18px;
}

.font-20 {
  font-size: 20px;
}

.font-32 {
  font-size: 32px;
}

.font-40 {
  font-size: 40px;
}

.no-border {
  border: none;
}

.bx-shadow {
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}

.thumb-sm {
  height: 32px;
  width: 32px;
}

.thumb-md {
  height: 48px;
  width: 48px;
}

.thumb-lg {
  height: 88px;
  width: 88px;
}

.h-150 {
  height: 150px;
}

.h-200 {
  height: 200px;
}

.h-300 {
  height: 300px;
}

.h-400 {
  height: 400px;
}

.w-200 {
  width: 200px;
}

/* ==============
  Waves Effect
===================*/
/*!
 * Waves v0.6.0
 * http://fian.my.id/Waves
 *
 * Copyright 2014 Alfiana E. Sibuea and other contributors
 * Released under the MIT license
 * https://github.com/fians/Waves/blob/master/LICENSE
 */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  vertical-align: middle;
  z-index: 1;
  will-change: opacity, transform;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  -webkit-transition: all 0.7s ease-out;
  transition: all 0.7s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0);
  transform: scale(0);
  pointer-events: none;
}

.waves-effect.waves-light .waves-ripple {
  background-color: rgba(255, 255, 255, 0.25);
}

.waves-effect.waves-red .waves-ripple {
  background-color: rgba(244, 67, 54, 0.7);
}

.waves-effect.waves-yellow .waves-ripple {
  background-color: rgba(255, 235, 59, 0.7);
}

.waves-effect.waves-orange .waves-ripple {
  background-color: rgba(255, 152, 0, 0.7);
}

.waves-effect.waves-purple .waves-ripple {
  background-color: rgba(156, 39, 176, 0.7);
}

.waves-effect.waves-green .waves-ripple {
  background-color: rgba(76, 175, 80, 0.7);
}

.waves-effect.waves-teal .waves-ripple {
  background-color: rgba(0, 150, 136, 0.7);
}

.waves-notransition {
  -webkit-transition: none;
  transition: none;
}

.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
  -webkit-mask-image: none;
}

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom;
}

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
}

.waves-block {
  display: block;
}

/* ==============
  Dashboard
===================*/
.dashboard .card{
  border-radius: 20px;
  height: 150px;
}
.dash-map {
  height: 235px;
}

#donut-example {
  height: 290px;
}

.area-chart-map {
  height: 200px;
  margin: 0 -26px -24px -24px;
}

.new-user table td img,
.new-user ul li img {
  margin-right: 8px;
  width: 36px;
}

.new-user table td .img-flag,
.new-user ul li .img-flag {
  width: 22px;
}

/* ==============
 Demo Only css
===================*/
.button-items {
  margin-bottom: -8px;
}

.button-items .btn {
  margin-bottom: 8px;
  margin-right: 5px;
}

.bs-example-modal {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  z-index: 1;
  display: block;
}

.icon-demo-content {
  text-align: center;
  color: #a1a7cc;
}

.icon-demo-content i {
  display: block;
  font-size: 28px;
  margin-bottom: 5px;
}

.icon-demo-content .col-sm-6 {
  margin-bottom: 30px;
}

.icon-demo-content .col-sm-6:hover i {
  color: #067bbc;
}

/*===grid===*/
.grid-col div span {
  padding-top: 10px;
  padding-bottom: 10px;
  margin-bottom: 15px;
  background: #eff3f6;
  border: 1px solid;
  border-color: #ced4da;
  display: block;
}

.grid-col .nested-col.row span {
  background-color: #eff3f6;
  display: block;
}

/* ==============
  Buttons
===================*/
.btn {
  border-radius: 3px;
  font-size: 14px;
}

.btn-round {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding-top: 8px;
}

.button-list,
.button-items {
  margin-bottom: -8px;
}

.button-list .btn,
.button-items .btn {
  margin-bottom: 8px;
  margin-right: 5px;
}

.btn-primary, .btn-success, .btn-info, .btn-warning, .btn-secondary,
.btn-danger, .btn-dark {
  color: #ffffff;
}

.btn-secondary {
  border-color: rgba(45, 59, 72, 0.2);
}

.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active, .btn-secondary.active,
.btn-secondary.focus, .btn-secondary:active, .btn-secondary:focus, .btn-secondary:hover,
.open > .dropdown-toggle.btn-secondary, .btn-secondary.active, .btn-secondary:active,
.show > .btn-secondary.dropdown-toggle {
  border: 1px solid rgba(45, 59, 72, 0.2);
}

.btn-primary {
  background-color: #0671b1;
  border: 1px solid #0671b1;
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle,
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active,
.btn-primary.focus, .btn-primary:active, .btn-primary:focus, .btn-primary:hover,
.open > .dropdown-toggle.btn-primary, .btn-outline-primary.active, .btn-outline-primary:active,
.show > .btn-outline-primary.dropdown-toggle, .btn-outline-primary:hover, .btn-primary.active,
.btn-primary:active, .show > .btn-primary.dropdown-toggle {
 background-color: #0671b1;
  border: 1px solid #0671b1;
  color: #ffffff;
  -webkit-box-shadow: 0 14px 26px -12px rgba(116, 96, 238, 0.42), 0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(116, 96, 238, 0.2);
   box-shadow: 0 14px 26px -12px rgba(116, 96, 238, 0.42), 0 4px 23px 0 rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(116, 96, 238, 0.2); 
}

.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus,
.btn-primary.focus,
.btn-primary:focus {
 /* -webkit-box-shadow: 0 0 0 0.2rem rgba(36, 44, 109, 0.5);
          box-shadow: 0 0 0 0.2rem rgba(36, 44, 109, 0.5);*/
}

.btn-success {
  background-color: #29b348;
  border: 1px solid #29b348;
}

.btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active,
.btn-success.focus, .btn-success:active, .btn-success:focus, .btn-success:hover,
.open > .dropdown-toggle.btn-success, .btn-outline-success.active, .btn-outline-success:active,
.show > .btn-outline-success.dropdown-toggle, .btn-outline-success:hover, .btn-success.active,
.btn-success:active, .show > .btn-success.dropdown-toggle {
  background-color: #249e40;
  border: 1px solid #249e40;
}

.btn-info {
  background-color: #44a2d2;
  border: 1px solid #44a2d2;
}

.btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .btn-info.focus,
.btn-info:active, .btn-info:focus, .btn-info:hover, .open > .dropdown-toggle.btn-info,
.btn-outline-info.active, .btn-outline-info:active,
.show > .btn-outline-info.dropdown-toggle, .btn-outline-info:hover, .btn-info.active, .btn-info:active,
.show > .btn-info.dropdown-toggle {
  background-color: #3197cc;
  border: 1px solid #3197cc;
}

.btn-warning {
  background-color: #f5b225;
  border: 1px solid #f5b225;
}

.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active,
.btn-warning.focus, .btn-warning:active, .btn-warning:focus, .btn-warning:hover,
.open > .dropdown-toggle.btn-warning, .btn-outline-warning.active, .btn-outline-warning:active,
.show > .btn-outline-warning.dropdown-toggle, .btn-outline-warning:hover, .btn-warning.active,
.btn-warning:active, .show > .btn-warning.dropdown-toggle {
  background-color: #f4a90d;
  border: 1px solid #f4a90d;
}

.btn-danger {
  background-color: #ec536c;
  border: 1px solid #ec536c;
}

.btn-danger:active, .btn-danger:focus, .btn-danger:hover, .btn-danger.active,
.btn-danger.focus, .btn-danger:active, .btn-danger:focus, .btn-danger:hover,
.open > .dropdown-toggle.btn-danger, .btn-outline-danger.active, .btn-outline-danger:active,
.show > .btn-outline-danger.dropdown-toggle, .btn-outline-danger:hover, .btn-danger.active,
.btn-danger:active, .show > .btn-danger.dropdown-toggle {
  background-color: #e93c58;
  border: 1px solid #e93c58;
}

.btn-dark {
  background-color: #2d3b48;
  border: 1px solid #2d3b48;
  color: #ffffff;
}

.btn-dark:hover, .btn-dark:focus, .btn-dark:active, .btn-dark.active, .btn-dark.focus,
.btn-dark:active, .btn-dark:focus, .btn-dark:hover, .open > .dropdown-toggle.btn-dark,
.btn-outline-dark.active, .btn-outline-dark:active,
.show > .btn-outline-dark.dropdown-toggle, .btn-outline-dark:hover {
  background-color: #232e38;
  border: 1px solid #232e38;
  color: #ffffff;
}

.btn-dark.focus, .btn-dark:focus, .btn-outline-dark.focus, .btn-outline-dark:focus {
  -webkit-box-shadow: 0 0 0 2px rgba(45, 59, 72, 0.3);
  box-shadow: 0 0 0 2px rgba(45, 59, 72, 0.3);
}

.btn-link {
  color: #2d3b48;
}

.btn-link:hover {
  color: #067bbc;
}

/* button Outline */
.btn-outline-primary {
  color: #067bbc;
  border-color: #067bbc;
}

.btn-outline-success {
  color: #29b348;
  border-color: #29b348;
}

.btn-outline-info {
  color: #44a2d2;
  border-color: #44a2d2;
}

.btn-outline-warning {
  color: #f5b225;
  border-color: #f5b225;
}

.btn-outline-danger {
  color: #ec536c;
  border-color: #ec536c;
}

.btn-outline-dark {
  color: #2d3b48;
  background-image: none;
  background-color: transparent;
  border-color: #2d3b48;
}

/* Social Buttons */
.btn-facebook {
  color: #ffffff !important;
  background-color: #3b5998;
}

.btn-twitter {
  color: #ffffff !important;
  background-color: #00aced;
}

.btn-linkedin {
  color: #ffffff !important;
  background-color: #007bb6;
}

.btn-dribbble {
  color: #ffffff !important;
  background-color: #ea4c89;
}

.btn-googleplus {
  color: #ffffff !important;
  background-color: #dd4b39;
}

.btn-instagram {
  color: #ffffff !important;
  background-color: #517fa4;
}

.btn-pinterest {
  color: #ffffff !important;
  background-color: #cb2027;
}

.btn-dropbox {
  color: #ffffff !important;
  background-color: #007ee5;
}

.btn-flickr {
  color: #ffffff !important;
  background-color: #ff0084;
}

.btn-tumblr {
  color: #ffffff !important;
  background-color: #32506d;
}

.btn-skype {
  color: #ffffff !important;
  background-color: #00aff0;
}

.btn-youtube {
  color: #ffffff !important;
  background-color: #bb0000;
}

.btn-github {
  color: #ffffff !important;
  background-color: #171515;
}

/* ==============
  Card
===================*/
.card {
  border: none;
  -webkit-box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.05);
          box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.card-primary {
  background-color: #067bbc;
  border-color: #067bbc;
}

.card-success {
  background-color: #29b348;
  border-color: #29b348;
}

.card-info {
  background-color: #44a2d2;
  border-color: #44a2d2;
}

.card-warning {
  background-color: #f5b225;
  border-color: #f5b225;
}

.card-danger {
  background-color: #ec536c;
  border-color: #ec536c;
}

.card-header {
  border-bottom: 1px solid rgba(45, 59, 72, 0.05);
}

/* ==============
  Pagination
===================*/
.page-link {
  color: #067bbc;
}

.page-item.active .page-link {
  background-color: #067bbc;
  border-color: #067bbc;
}

.page-link:focus, .page-link:hover {
  color: #2d3b48;
  background-color: #eff3f6;
}

/* ==============
  Progressbar
===================*/
.h-progress .progress {
  border-radius: 50px;
}

.h-progress .progress .progress-bar:last-child {
  border-radius: 0 60px 60px 0;
}

.v-progress .progress {
  border-radius: 50px;
}

.v-progress .progress .progress-bar:last-child {
  border-radius: 60px 60px 0 0;
}

.v-progress .v-striped .progress-bar:last-child {
  border-radius: 0 0 60px 60px;
}

.progress-bar {
  background-color: #067bbc;
}

.bg-progress {
  background-color: #606bca;
}

/* Progressbar Vertical */
.progress-vertical {
  min-height: 250px;
  height: 250px;
  width: 10px;
  position: relative;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}

.progress-vertical .progress-bar {
  width: 100%;
}

.progress-vertical-bottom {
  min-height: 250px;
  height: 250px;
  position: relative;
  width: 10px;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}

.progress-vertical-bottom .progress-bar {
  width: 100%;
  position: absolute;
  bottom: 0;
}

.progress-vertical.progress-sm,
.progress-vertical-bottom.progress-sm {
  width: 5px !important;
}

.progress-vertical.progress-sm .progress-bar,
.progress-vertical-bottom.progress-sm .progress-bar {
  font-size: 8px;
  line-height: 5px;
}

.progress-vertical.progress-md,
.progress-vertical-bottom.progress-md {
  width: 15px !important;
}

.progress-vertical.progress-md .progress-bar,
.progress-vertical-bottom.progress-md .progress-bar {
  font-size: 10.8px;
  line-height: 14.4px;
}

.progress-vertical.progress-lg,
.progress-vertical-bottom.progress-lg {
  width: 20px !important;
}

.progress-vertical.progress-lg .progress-bar,
.progress-vertical-bottom.progress-lg .progress-bar {
  font-size: 12px;
  line-height: 20px;
}

/* ==============
  Popover & Tooltips
===================*/
.popover-title {
  margin-top: 0;
}

.tooltip .tooltip-inner {
  padding: 4px 10px;
}

/* =========== */
/* Sweet Alert */
/* =========== */
.swal2-modal {
  font-family: "Work Sans", sans-serif;
}

.swal2-modal .swal2-title {
  font-size: 28px;
}

.swal2-modal .swal2-content {
  font-size: 16px;
}

.swal2-modal .swal2-spacer {
  margin: 10px 0;
}

.swal2-modal .swal2-file, .swal2-modal .swal2-input, .swal2-modal .swal2-textarea {
  border: 2px solid #a1a7cc;
  font-size: 16px;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.swal2-modal .swal2-styled {
  border: inherit;
}

.swal2-icon.swal2-question {
  color: #067bbc;
  border-color: #067bbc;
}

.swal2-icon.swal2-success {
  border-color: #29b348;
}

.swal2-icon.swal2-success .line {
  background-color: #29b348;
}

.swal2-icon.swal2-success .placeholder {
  border-color: #29b348;
}

.swal2-icon.swal2-warning {
  color: #f5b225;
  border-color: #f5b225;
}

.swal2-icon.swal2-error {
  border-color: #ec536c;
}

.swal2-icon.swal2-error .line {
  background-color: #ec536c;
}

.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {
  outline: 0;
  border: 2px solid #067bbc;
}

/* ==============
  Background
==================*/
.bg-primary {
  background-color: #067bbc !important;
}

.bg-secondary {
  background-color: #6887bb !important;
}

.bg-success {
  background-color: #29b348 !important;
}

.bg-warning {
  background-color: #f5b225 !important;
}

.bg-info {
  background-color: #44a2d2 !important;
}

.bg-danger {
  background-color: #ec536c !important;
}

.bg-dark {
  background-color: #2d3b48 !important;
}

.bg-light {
  background-color: #eff3f6 !important;
}
.badge-success {
  adding: 11px 14px 10px 14px;
    margin: 10px;
}
.bg-pink {
  background-color: #ec408f !important;
}

.bg-purple {
  background-color: #7043c1 !important;
}

.bg-soft-primary {
  background-color: rgba(36, 44, 109, 0.15) !important;
  color: #067bbc !important;
}

.bg-soft-secondary {
  background-color: rgba(104, 135, 187, 0.15) !important;
  color: #6887bb !important;
}

.bg-soft-success {
  background-color: rgba(41, 179, 72, 0.15) !important;
  color: #29b348 !important;
}

.bg-soft-warning {
  background-color: rgba(245, 178, 37, 0.15) !important;
  color: #f5b225 !important;
}

.bg-soft-info {
  background-color: rgba(68, 162, 210, 0.15) !important;
  color: #44a2d2 !important;
}

.bg-soft-danger {
  background-color: rgba(236, 83, 108, 0.15) !important;
  color: #ec536c !important;
}

.bg-soft-pink {
  background-color: rgba(236, 64, 143, 0.15) !important;
  color: #ec408f !important;
}

.bg-soft-purple {
  background-color: rgba(112, 67, 193, 0.15) !important;
  color: #7043c1 !important;
}

.bg-soft-dark {
  background-color: rgba(45, 59, 72, 0.15) !important;
  color: #2d3b48 !important;
}

/* ==============
  Alertify
===================*/
.alertify, .alertify-logs {
  z-index: 99;
}

.alertify input {
  border: 2px solid #a1a7cc;
}

.alertify-logs > .success {
  background-color: #29b348;
  color: #ffffff;
}

.alertify-logs > .error {
  background-color: #ec536c;
  color: #ffffff;
}

.alertify-logs > *, .alertify-logs > .default {
  background-color: #2d3b48;
}

/* ==============
  Charts
===================*/
.jqstooltip {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}

.chart {
  position: relative;
  display: inline-block;
/*  width: 110px;*/
  height: 110px;
  margin-top: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.chart canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.chart.chart-widget-pie {
  margin-top: 5px;
  margin-bottom: 5px;
}

.percent {
  display: inline-block;
  line-height: 110px;
  z-index: 2;
}

.percent:after {
  content: '%';
  margin-left: 0.1em;
  font-size: .8em;
}

/* Morris chart */
.morris-hover.morris-default-style {
  border-radius: 5px;
  padding: 10px 12px;
  background: #ffffff;
  border: none;
  font-family: "Work Sans", sans-serif;
  -webkit-box-shadow: 0 5px 25px 5px rgba(0, 0, 0, 0.14);
          box-shadow: 0 5px 25px 5px rgba(0, 0, 0, 0.14);
}

.morris-hover.morris-default-style .morris-hover-point {
  font-weight: 500;
  font-size: 14px;
  color: #2d3b48 !important;
}

.morris-hover.morris-default-style .morris-hover-row-label {
  background-color: #2d3b48;
  color: #ffffff;
  padding: 4px;
  border-radius: 5px 5px 0 0;
  margin: -10px -12px 10px;
}

/* Flot chart */
#flotTip {
  padding: 8px 12px;
  background-color: #ffffff;
  z-index: 100;
  color: #2d3b48;
  -webkit-box-shadow: 0 5px 25px 5px rgba(0, 0, 0, 0.14);
          box-shadow: 0 5px 25px 5px rgba(0, 0, 0, 0.14);
  border-radius: 1px;
}

/* Chartist chart */
.ct-golden-section:before {
  float: none;
}

.ct-chart {
  height: 300px;
}

.ct-grid {
  stroke: rgba(0, 0, 0, 0.09);
  stroke-width: 2px;
  stroke-dasharray: 3px;
}

.ct-chart .ct-label {
  fill: #a1a7cc;
  color: #a1a7cc;
  font-size: 14px;
  line-height: 1;
}

.ct-chart.simple-pie-chart-chartist .ct-label {
  color: #ffffff;
  fill: #ffffff;
  font-size: 16px;
}

.ct-chart .ct-series.ct-series-a .ct-bar,
.ct-chart .ct-series.ct-series-a .ct-line,
.ct-chart .ct-series.ct-series-a .ct-point,
.ct-chart .ct-series.ct-series-a .ct-slice-donut {
  stroke: #067bbc;
}

.ct-chart .ct-series.ct-series-b .ct-bar,
.ct-chart .ct-series.ct-series-b .ct-line,
.ct-chart .ct-series.ct-series-b .ct-point,
.ct-chart .ct-series.ct-series-b .ct-slice-donut {
  stroke: #44a2d2;
}

.ct-chart .ct-series.ct-series-c .ct-bar,
.ct-chart .ct-series.ct-series-c .ct-line,
.ct-chart .ct-series.ct-series-c .ct-point,
.ct-chart .ct-series.ct-series-c .ct-slice-donut {
  stroke: #f5b225;
}

.ct-chart .ct-series.ct-series-d .ct-bar,
.ct-chart .ct-series.ct-series-d .ct-line,
.ct-chart .ct-series.ct-series-d .ct-point,
.ct-chart .ct-series.ct-series-d .ct-slice-donut {
  stroke: #2d3b48;
}

.ct-chart .ct-series.ct-series-e .ct-bar,
.ct-chart .ct-series.ct-series-e .ct-line,
.ct-chart .ct-series.ct-series-e .ct-point,
.ct-chart .ct-series.ct-series-e .ct-slice-donut {
  stroke: #29b348;
}

.ct-chart .ct-series.ct-series-f .ct-bar,
.ct-chart .ct-series.ct-series-f .ct-line,
.ct-chart .ct-series.ct-series-f .ct-point,
.ct-chart .ct-series.ct-series-f .ct-slice-donut {
  stroke: #ec536c;
}

.ct-chart .ct-series.ct-series-g .ct-bar,
.ct-chart .ct-series.ct-series-g .ct-line,
.ct-chart .ct-series.ct-series-g .ct-point,
.ct-chart .ct-series.ct-series-g .ct-slice-donut {
  stroke: #eff3f6;
}

.ct-series-a .ct-area,
.ct-series-a .ct-slice-pie {
  fill: #067bbc;
}

.ct-series-b .ct-area,
.ct-series-b .ct-slice-pie {
  fill: #f5b225;
}

.ct-series-c .ct-area,
.ct-series-c .ct-slice-pie {
  fill: #2d3b48;
}

.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 10px;
  padding: 2px 10px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  background-clip: padding-box;
  background: #2d3b48;
  color: #ffffff;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity .2s linear;
  transition: opacity .2s linear;
}

.chartist-tooltip.tooltip-show {
  opacity: 1;
}

/* C3 chart */
.c3 svg {
  max-width: 100%;
}

.c3-tooltip td > span {
  background: #2d3b48;
}

.c3-tooltip td {
  border-left: none;
}

.c3-tooltip {
  -webkit-box-shadow: 0 8px 40px 0 rgba(0, 0, 0, 0.12);
          box-shadow: 0 8px 40px 0 rgba(0, 0, 0, 0.12);
  opacity: 1;
}

.c3-chart-arcs-title {
  font-size: 18px;
  font-weight: 600;
  font-family: "Work Sans", sans-serif;
}

.c3-tooltip tr {
  border: none !important;
}

.c3-tooltip th {
  background-color: #2d3b48;
}

.c3-tooltip .value {
  font-weight: 600;
  font-family: "Work Sans", sans-serif;
}

.c3-line {
  stroke-width: 2px;
}

.c3-legend-item {
  font-size: 13px;
  font-family: "Work Sans", sans-serif;
}

/* ==============
  Tables
===================*/
.table {
  margin-bottom: 10px;
}

th {
  font-weight: 500;
}

.table > tbody > tr > td, .table > tfoot > tr > td, .table > thead > tr > td {
  padding: 8px 12px;
  vertical-align: middle;
}

.table-hover tbody tr:hover, .table-striped tbody tr:nth-of-type(odd),
.thead-default th {
  background-color: #f8f9fa;
}

/* == Responsive Table ==*/
table.focus-on tbody tr.focused th {
  background-color: #067bbc;
  color: #ffffff;
}

table.focus-on tbody tr.focused td {
  background-color: #067bbc;
  color: #ffffff;
}

.table-rep-plugin .btn-toolbar {
  display: block;
}

.table-rep-plugin .btn-default {
  background-color: white;
  border-color: #dee2e6;
  margin-right: 5px;
}

.table-rep-plugin .btn-default.btn-primary {
  background-color: #067bbc;
  border-color: #067bbc;
}

.table-rep-plugin .table-responsive {
  border: none !important;
}

.table-rep-plugin .btn-group.pull-right .dropdown-menu {
  left: auto;
  right: 0;
}

.table-rep-plugin tbody th {
  font-size: 14px;
  font-weight: normal;
}

.table-rep-plugin .checkbox-row {
  padding-left: 40px;
}

.table-rep-plugin .checkbox-row label {
  display: inline-block;
  padding-left: 5px;
  position: relative;
}

.table-rep-plugin .checkbox-row label::before {
  -o-transition: 0.3s ease-in-out;
  -webkit-transition: 0.3s ease-in-out;
  background-color: #ffffff;
  border-radius: 3px;
  border: 1px solid #ced4da;
  content: "";
  display: inline-block;
  height: 17px;
  left: 0;
  margin-left: -20px;
  position: absolute;
  transition: 0.3s ease-in-out;
  width: 17px;
  outline: none !important;
}

.table-rep-plugin .checkbox-row label::after {
  color: #5d5b6f;
  display: inline-block;
  font-size: 11px;
  height: 16px;
  left: 0;
  margin-left: -20px;
  padding-left: 3px;
  padding-top: 1px;
  position: absolute;
  top: -1px;
  width: 16px;
}

.table-rep-plugin .checkbox-row input[type="checkbox"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important;
}

.table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label {
  opacity: 0.65;
}

.table-rep-plugin .checkbox-row input[type="checkbox"]:focus + label::before {
  outline-offset: -2px;
  outline: none;
}

.table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
  content: "\f00c";
  font-family: 'FontAwesome';
}

.table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label::before {
  background-color: #eff3f6;
  cursor: not-allowed;
}

.table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::before {
  background-color: #067bbc;
  border-color: #067bbc;
}

.table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
  color: #ffffff;
}

.dataTables_wrapper.container-fluid {
  width: 100%;
  padding: 0;
}

/* ==============
  Range slider
===================*/
.irs-from, .irs-to, .irs-single {
  background: #067bbc;
}

.irs-from:after, .irs-to:after, .irs-single:after {
  border-top-color: #067bbc;
}

#flat-slider.ui-slider {
  background: #ced4da;
  border: none;
  border-radius: 0;
}

#flat-slider.ui-slider .ui-slider-handle {
  width: 20px;
  height: 20px;
  border-radius: 50% 50% 0;
  border-color: transparent;
  -webkit-transition: border 0.4s ease;
  transition: border 0.4s ease;
}

#flat-slider.ui-slider .ui-slider-handle.ui-state-hover,
#flat-slider.ui-slider .ui-slider-handle.ui-state-focus,
#flat-slider.ui-slider .ui-slider-handle.ui-state-active {
  border-color: #2d3b48;
}

#flat-slider.ui-slider .ui-slider-pip .ui-slider-line {
  background: #ced4da;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

#flat-slider.ui-slider.ui-slider-horizontal {
  height: 6px;
}

#flat-slider.ui-slider.ui-slider-horizontal .ui-slider-handle {
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
  top: -25px;
  margin-left: -10px;
}

#flat-slider.ui-slider.ui-slider-horizontal .ui-slider-pip {
  top: 10px;
}

#flat-slider.ui-slider.ui-slider-horizontal .ui-slider-pip .ui-slider-line {
  width: 2px;
  height: 10px;
  margin-left: -1px;
}

#flat-slider.ui-slider.ui-slider-horizontal .ui-slider-pip[class*=ui-slider-pip-selected] .ui-slider-line {
  height: 20px;
}

#flat-slider.ui-slider.ui-slider-horizontal .ui-slider-pip.ui-slider-pip-inrange .ui-slider-line {
  height: 12px;
}

#flat-slider.ui-slider.ui-slider-vertical {
  width: 6px;
  height: 125px;
  display: inline-block;
  margin: 0 15%;
}

#flat-slider.ui-slider.ui-slider-vertical .ui-slider-handle {
  -webkit-transform: rotateZ(-45deg);
  transform: rotateZ(-45deg);
  left: -25px;
  margin-bottom: -10px;
}

#flat-slider.ui-slider.ui-slider-vertical .ui-slider-pip {
  left: 10px;
}

#flat-slider.ui-slider.ui-slider-vertical .ui-slider-pip .ui-slider-line {
  height: 2px;
  width: 10px;
  margin-top: -1px;
}

#flat-slider.ui-slider.ui-slider-vertical .ui-slider-pip[class*=ui-slider-pip-selected] .ui-slider-line {
  width: 20px;
}

#flat-slider.ui-slider.ui-slider-vertical .ui-slider-pip.ui-slider-pip-inrange .ui-slider-line {
  width: 12px;
}

#flat-slider.ui-slider.ui-slider-vertical .ui-slider-handle .ui-slider-tip,
#flat-slider.ui-slider.ui-slider-vertical .ui-slider-handle[class*=ui-state-] .ui-slider-tip {
  visibility: visible;
  opacity: 1;
  border: none;
  background: transparent;
  left: 50%;
  width: 30px;
  margin-left: -15px;
  text-align: center;
  color: #ffffff;
  font-weight: normal;
  top: 10px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}

#flat-slider.ui-slider.ui-slider-vertical .ui-slider-handle .ui-slider-tip:before,
#flat-slider.ui-slider.ui-slider-vertical .ui-slider-handle[class*=ui-state-] .ui-slider-tip:before {
  display: none;
}

#flat-slider .ui-slider-handle,
#flat-slider .ui-slider-range,
#flat-slider .ui-slider-pip[class*=ui-slider-pip-selected] .ui-slider-line,
#flat-slider .ui-slider-pip.ui-slider-pip-inrange .ui-slider-line {
  background-color: #44a2d2;
}

#flat-slider-vertical-1 .ui-slider-handle,
#flat-slider-vertical-1 .ui-slider-range,
#flat-slider-vertical-1 .ui-slider-pip[class*=ui-slider-pip-selected] .ui-slider-line,
#flat-slider-vertical-1 .ui-slider-pip.ui-slider-pip-inrange .ui-slider-line {
  background-color: #067bbc;
}

#flat-slider-vertical-2 .ui-slider-handle,
#flat-slider-vertical-2 .ui-slider-range,
#flat-slider-vertical-2 .ui-slider-pip[class*=ui-slider-pip-selected] .ui-slider-line,
#flat-slider-vertical-2 .ui-slider-pip.ui-slider-pip-inrange .ui-slider-line {
  background-color: #29b348;
}

#flat-slider-vertical-3 .ui-slider-handle,
#flat-slider-vertical-3 .ui-slider-range,
#flat-slider-vertical-3 .ui-slider-pip[class*=ui-slider-pip-selected] .ui-slider-line,
#flat-slider-vertical-3 .ui-slider-pip.ui-slider-pip-inrange .ui-slider-line {
  background-color: #44a2d2;
}

[id*=flat-slider].ui-slider.ui-slider-vertical {
  width: 6px;
  height: 125px;
  display: inline-block;
  margin: 0 15%;
}

[id*=flat-slider].ui-slider {
  background: #ced4da;
  border: none;
  border-radius: 0;
}

[id*=flat-slider].ui-slider.ui-slider-vertical .ui-slider-handle .ui-slider-tip, [id*=flat-slider].ui-slider.ui-slider-vertical .ui-slider-handle[class*=ui-state-] .ui-slider-tip {
  visibility: visible;
  opacity: 1;
  border: none;
  background: 0 0;
  left: 50%;
  width: 30px;
  margin-left: -15px;
  text-align: center;
  color: #ffffff;
  font-weight: 400;
  top: 10px;
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}

[id*=flat-slider].ui-slider.ui-slider-vertical .ui-slider-handle {
  -webkit-transform: rotateZ(-45deg);
  transform: rotateZ(-45deg);
  left: -25px;
  margin-bottom: -10px;
}

[id*=flat-slider].ui-slider .ui-slider-handle {
  width: 20px;
  height: 20px;
  border-radius: 50% 50% 0;
  border-color: transparent;
  -webkit-transition: border .4s ease;
  transition: border .4s ease;
}

[id*=flat-slider].ui-slider.ui-slider-vertical .ui-slider-pip.ui-slider-pip-inrange .ui-slider-line {
  width: 12px;
}

[id*=flat-slider].ui-slider.ui-slider-vertical .ui-slider-pip .ui-slider-line {
  height: 2px;
  width: 10px;
  margin-top: -1px;
}

[id*=flat-slider].ui-slider.ui-slider-vertical .ui-slider-pip[class*=ui-slider-pip-selected] .ui-slider-line {
  width: 20px;
}

[id*=flat-slider].ui-slider .ui-slider-handle,
[id*=flat-slider].ui-slider .ui-slider-handle.ui-state-active,
[id*=flat-slider].ui-slider .ui-slider-handle.ui-state-focus,
[id*=flat-slider].ui-slider .ui-slider-handle.ui-state-hover {
  border-color: #2d3b48;
}

/*===================*/
#alternating-slider .ui-slider-pip .ui-slider-line {
  height: 8px;
  top: -5px;
}

#alternating-slider .ui-slider-pip:nth-child(even) {
  top: -33px;
  height: 15px;
  -webkit-transform: scale(0.8) translateY(3px);
          transform: scale(0.8) translateY(3px);
  opacity: 0.8;
}

#alternating-slider .ui-slider-pip:nth-child(even) .ui-slider-line {
  top: 22px;
  height: 9px;
}

#alternating-slider .ui-slider-pip:nth-child(even).ui-slider-pip-selected-1 .ui-slider-label,
#alternating-slider .ui-slider-pip:nth-child(even).ui-slider-pip-selected-2 .ui-slider-label,
#alternating-slider .ui-slider-pip:nth-child(even).ui-slider-pip-selected-3 .ui-slider-label,
#alternating-slider .ui-slider-pip:nth-child(even).ui-slider-pip-selected-4 .ui-slider-label {
  top: -3px;
}

#alternating-slider .ui-slider-pip-initial-1 .ui-slider-label {
  color: #44a2d2;
}

#alternating-slider .ui-slider-pip-initial-2 .ui-slider-label {
  color: #44a2d2;
}

#alternating-slider .ui-slider-pip-initial-3 .ui-slider-label {
  color: #29b348;
}

#alternating-slider .ui-slider-pip-initial-4 .ui-slider-label {
  color: #2ec850;
}

#alternating-slider .ui-slider-pip-selected-1 .ui-slider-label,
#alternating-slider .ui-slider-pip-selected-2 .ui-slider-label,
#alternating-slider .ui-slider-pip-selected-3 .ui-slider-label,
#alternating-slider .ui-slider-pip-selected-4 .ui-slider-label {
  color: #ffffff;
  width: 2.4em;
  padding: 4px 0;
  margin-left: -1.2em;
  border-radius: 2px;
}

#alternating-slider .ui-slider-pip-selected-1 .ui-slider-label {
  background-color: #44a2d2;
}

#alternating-slider .ui-slider-pip-selected-2 .ui-slider-label {
  background-color: #44a2d2;
}

#alternating-slider .ui-slider-pip-selected-3 .ui-slider-label {
  background-color: #29b348;
}

#alternating-slider .ui-slider-pip-selected-4 .ui-slider-label {
  background-color: #2ec850;
}

#alternating-slider .ui-slider-tip {
  width: 34px;
  margin-left: -17px;
  top: -1px;
  background: #44a2d2;
  color: #ffffff;
  border: none;
  line-height: 27px;
  height: 25px;
}

#alternating-slider .ui-slider-tip:before,
#alternating-slider .ui-slider-tip:after {
  display: none;
}

#alternating-slider .ui-slider-handle.ui-state-active .ui-slider-tip,
#alternating-slider .ui-slider-handle.ui-state-focus .ui-slider-tip,
#alternating-slider .ui-slider-handle.ui-state-hover .ui-slider-tip,
#alternating-slider .ui-slider-handle:focus .ui-slider-tip,
#alternating-slider .ui-slider-handle:hover .ui-slider-tip {
  top: -1px;
}

#alternating-slider .ui-slider-handle.ui-state-focus {
  z-index: 100;
}

#alternating-slider .ui-slider-tip {
  width: 34px;
  margin-left: -17px;
  top: -1px;
  background: #44a2d2;
  color: #ffffff;
  border: none;
  line-height: 27px;
  height: 25px;
}

#alternating-slider .ui-slider-pip-selected-1 .ui-slider-label {
  background-color: #067bbc;
}

.ui-widget-content .ui-slider-handle.ui-state-default {
  background: #f5b225;
  border-color: #f5b225;
}

.ui-slider.ui-slider-horizontal .ui-slider-handle {
  width: 15px;
  height: 25px;
  margin-left: -7px;
  top: -7px;
}

.irs--flat .irs-handle.state_hover > i:first-child,
.irs--flat .irs-handle:hover > i:first-child,
.irs--flat .irs-handle > i:first-child,
.irs--flat .irs-from,
.irs--flat .irs-to,
.irs--flat .irs-single,
.irs--flat .irs-bar {
  background-color: #067bbc;
}

.irs--flat .irs-from:before,
.irs--flat .irs-to:before,
.irs--flat .irs-single:before {
  border-top-color: #067bbc;
}

.irs--sharp .irs-bar,
.irs--sharp .irs-handle {
  background-color: #29b348;
}

.irs--sharp .irs-handle > i:first-child {
  border-top-color: #29b348;
}

.irs--sharp .irs-line {
  background-color: #dee2e6;
}

.irs--square .irs-handle {
  border-color: #ec408f;
}

.irs--square .irs-from,
.irs--square .irs-to,
.irs--square .irs-single,
.irs--square .irs-bar {
  background-color: #ec408f;
}

/*
File: Menu
*/
.topbar .topbar-left {
  float: left;
  height: 70px;
  position: relative;
  width: 240px;
  z-index: 1;
  background-color: #ffffff;
}

.logo {
  line-height: 71px;

}
.front-logo{
  width: 70% !important;
  height: 80px;
}

.logo-large {
  width: 240px !important;
}

.left .topbar-left {
  background-color: #fff;
  height: 91px;
}

.has_sub.nav-active i.mdi-chevron-right:before {
  content: "\F140";
}

.navbar-custom {
  background-color: #fff;
  border: none;
  margin: -20px -25px 0 -25px;
  -webkit-box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.2);
          box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.2);
}

/* Notification */
.notification-list {
  margin: 0 !important;
}

.notification-list .noti-title {
  border-radius: 0.25rem 0.25rem 0 0;
  background-color: #067bbc;
  color: #ffffff;
  margin: -4px 0px 0px 0px;
  width: auto;
  padding: 12px 20px;
}

.notification-list .noti-title h5 {
  margin: 0;
  font-size: 14px;
}

.notification-list .noti-title .label {
  float: right;
}

.notification-list .noti-icon {
  font-size: 18px;
  vertical-align: middle;
  color: #ffffff;
}

.notification-list .noti-icon-badge {
  display: inline-block;
  position: absolute;
  top: 14px;
  right: 9px;
}

.notification-list .notify-item {
  padding: 15px 20px;
}

.notification-list .notify-item .notify-icon {
  float: left;
  height: 36px;
  width: 36px;
  line-height: 36px;
  text-align: center;
  margin-right: 10px;
  border-radius: 50%;
  color: #ffffff;
}

.notification-list .notify-item .notify-icon img {
  margin-top: 4px;
}

.notification-list .notify-item .notify-details {
  margin-bottom: 0;
  overflow: hidden;
  margin-left: 45px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-list .notify-item .notify-details b {
  font-weight: normal;
}

.notification-list .notify-item .notify-details small {
  display: block;
  white-space: normal;
}

.notification-list .notify-item .notify-details span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
}

.notification-list .notify-all {
  border-radius: 0 0 0.25rem 0.25rem;
  margin: 0 0 -5px 0;
  background-color: #eff3f6;
}

.notification-list .profile-dropdown .notify-item {
  padding: 4px 20px;
}

.notification-list .nav-link {
  padding: 0 15px;
  line-height: 70px;
}

.notification-list .language-switch a img {
  float: right;
  margin-top: 5px;
}

.profile-dropdown {
  width: auto;
}

.profile-dropdown i {
  font-size: 17px;
  vertical-align: middle;
  margin-right: 5px;
}

.profile-dropdown span {
  vertical-align: middle;
}

.nav-user {
  margin: 0 15px 0 0;
}

.nav-user img {
  height: 36px;
  width: 36px;
}

.arrow-none:after {
  border: none;
  margin: 0;
  display: none;
}

.menu-title {
  padding: 12px 25px !important;
  letter-spacing: .035em;
  pointer-events: none;
  cursor: default;
  font-size: 13px;
  color: #6c757d;
}

.profile {
  padding: 17px 15px !important;
}

.profile img {
  border: 2px solid #e9ecef;
  height: 36px;
  width: 36px;
  float: left;
}

.profile .profile-username {
  margin-left: 45px;
  display: block;
  line-height: 36px;
}

.dropdown-menu-lg {
  width: 270px;
}

.navbar-nav {
  margin: 0;
}

.side-menu {
  bottom: 0;
  top: 0;
  width: 240px;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  background: #ffffff;
  position: absolute;
  z-index: 99;
  -webkit-box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.05);
          box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.05);
}

body.fixed-left .side-menu.left {
  bottom: 50px;
  height: 100%;
  margin-bottom: -70px;
  margin-top: 0;
  padding-bottom: 70px;
  position: fixed;
}

.content-page {
  margin-left: 240px;
  overflow: hidden;
  min-height: 500px;
}

.content-page > .content {
  margin-bottom: 63px;
  padding: 0 15px 0 15px;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
}

.page-content-wrapper {
  margin: 0 -10px;
}

.button-menu-mobile {
  background-color: #067bbc;
  font-size: 28px;
  color: #ffffff;
  width: 42px;
  border-radius: 3px;
  border: none;
  line-height: 70px;
  display: none;
}

.button-menu-mobile-topbar {
  background-color: #ec536c;
  color: #ffffff;
  font-size: 20px;
  height: 42px;
  width: 42px;
  line-height: 42px;
  border-radius: 0;
  border: none;
  position: absolute;
  right: -42px;
  top: 5px;
}

.app-search .form-control, .app-search .form-control:focus {
  border: none;
  font-size: 13px;
  height: 34px;
  color: #ffffff;
  padding-left: 20px;
  padding-right: 40px;
  background: #313b93;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 30px;
  width: 200px;
}

.app-search {
  position: relative;
  padding-top: 18px;
  margin-left: 20px;
}

.app-search input.form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.app-search input.form-control:-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.app-search input.form-control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.app-search input.form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.app-search a {
  position: absolute;
  top: 18px;
  left: 160px;
  display: block;
  height: 34px;
  line-height: 34px;
  width: 34px;
  text-align: center;
  color: #ffffff;
}

.sidebar-inner {
  height: 100%;
}

.slimScrollDiv {
  z-index: -10;
  height: 890px !important;
}

.slimScrollBar,
.slimscrollleft {
  margin-top: 2%;
  height: 890px;
}

#sidebar-menu, #sidebar-menu ul, #sidebar-menu li, #sidebar-menu a {
  border: 0;
  font-weight: normal;
  line-height: 1;
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  text-decoration: none;
}

.navbar-nav > li > a.notification-icon-box {
  line-height: 70px;
}

.navbar-nav > li > a.notification-icon-box i {
  font-size: 26px;
  vertical-align: middle;
}

#sidebar-menu {
  background-color: #ffffff;
  padding-bottom: 100px;
  width: 100%;
  padding-top: 16px;
}

#sidebar-menu ul ul {
  display: none;
}

#sidebar-menu ul ul li {
  border-top: 0;
}

#sidebar-menu ul ul li.active a {
  color: #067bbc;
}

#sidebar-menu ul ul li.active a:before {
  content: "\F12F";
  font: normal normal normal 24px/1 "Material Design Icons";
  position: absolute;
  font-size: 8px;
  left: 55px;
  color: #067bbc;
  top: 15px;
}

#sidebar-menu ul ul a {
  color: rgba(45, 59, 72, 0.8);
  display: block;
  padding: 12px 25px 12px 78px;
  font-size: 14px;
}

#sidebar-menu ul ul a:hover {
  color: #2d3b48;
}

#sidebar-menu ul ul a i {
  margin-right: 5px;
}

#sidebar-menu ul ul ul a {
  padding-left: 80px;
}

.form-group {
  margin-bottom: 20px !important;
}
.tittle{

    background: #ffffff;
    margin: 0 0 50px;
    position: relative;
    margin-top: 1px;
    height: 60px;
}
.tittle .top h5{
  font-size: 17px;
  font-weight: 400;
  float: left!important;
}

#sidebar-menu > ul > li > a {
  color: #2d3b48;
  display: block;
  padding: 10px 25px;
  margin: 3px 9px;
  background-color: #ffffff;
  /*margin-left: 19px;*/
  border-radius: 15px 0 0 15px;
}

#sidebar-menu > ul > li > a:hover {
  color: #067bbc;
  text-decoration: none;
}

#sidebar-menu > ul > li > a:hover i {
  color: #067bbc;
}

#sidebar-menu > ul > li > a span i {
  font-size: 18px;
  line-height: 25px;
  margin-right: -18px;
}

#sidebar-menu > ul > li.nav-active > a {
  background-color: #067bbc21;
  border-right: 3px solid #067bbc;
  padding: 15px 25px;
}

#sidebar-menu > ul > li > a > span {
  vertical-align: middle;
}

#sidebar-menu > ul > li > a > i {
  display: inline-block;
  font-size: 18px;
  line-height: 25px;
  margin-left: 3px;
  margin-right: 10px;
  text-align: center;
  vertical-align: middle;
  width: 25px;
  height: 25px;
  background-color: #ffffff;
  border-radius: 3px;
  color: #6c757d;
}

#sidebar-menu > ul > li > a > i.i-right {
  float: right;
  margin: 3px 0 0 0;
}

#sidebar-menu > ul > li > a.active {
  color: #067bbc;
  background-color: #067bbc2b;
 margin:0 9px;
   border-radius: 15px 0 0 15px;
   padding: 15px 25px;
  border-right: 3px solid #067bbc;

}

#sidebar-menu > ul > li > a.active i {
  background-color: transparent;
  color: #067bbc;
  -webkit-box-shadow: none;
          box-shadow: none;
    border-radius: 15px 0 0 15px;
}

#sidebar-menu > ul > li > a.active .mdi-chevron-right {
  background-color: transparent;
  color: #067bbc;
}


#sidebar-menu > ul > li.nav-active > ul {
  display: block;
}

#wrapper.enlarged .left.side-menu {
  padding-top: 0;
  z-index: 1001;
  margin-left: -100%;
}

#wrapper.enlarged .content-page {
  margin-left: 0;
}

#wrapper.enlarged .footer {
  left: 0;
}

.user-details {
  min-height: 80px;
  padding: 20px;
  position: relative;
}

/* Calendar container */
.calendar {
  width: 800px;
  height: 600px;
  border: 1px solid black;
  margin: 0 auto;
}

/* Calendar header */
.calendar-header {
  background-color: #ccc;
  padding: 10px;
}

/* Calendar title */
.calendar-title {
  font-size: 20px;
  font-weight: bold;
}

/* Calendar events */
.calendar-event {
  padding: 10px;
  border-bottom: 1px solid black;
}

/* Calendar event title */
.calendar-event-title {
  font-size: 16px;
}

/* Calendar event start time */
.calendar-event-start-time {
  font-size: 12px;
}

/* Calendar event end time */
.calendar-event-end-time {
  font-size: 12px;
}

.user-details img {
  position: relative;
  z-index: 9999;
  height: 64px;
  width: 64px;
}

.user-details .user-info {
  text-align: center;
}

.user-details .user-info .user-status {
  display: inline-block;
  padding: 3px 10px;
  border: 1px solid rgba(34, 34, 34, 0.27);
  border-radius: 20px;
  font-size: 12px;
}

.page-title-box {
  padding: 22px 0;
}

.page-title-box .page-title {
  font-size: 20px;
  margin-bottom: 0;
  margin-top: 0;
}

.page-title-box .breadcrumb .breadcrumb-item a {
  color: #5d5b6f;
}

/* ==============
  Form-elements
===================*/
label {
  font-weight: 500;
}

.sub-title {
  font-weight: 500;
}

.form-control {
  font-size: 14px;
  width: 70%;
}

.btn-block {
border-radius: 20px;
padding: 12px;
background-color: #102b49;
width: 90%;
}
.btn-primary.btn-block:hover {
background-color: #102b49 !important;
border: 1px solid #102b49 !important;
}
.form-control:focus {
  border-color: #067bbc;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.custom-control-input:checked ~ .custom-control-indicator {
  background-color: #067bbc;
}

.custom-control-input:focus ~ .custom-control-indicator {
  -webkit-box-shadow: 0 0 0 1px #ffffff, 0 0 0 3px #067bbc;
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 3px #067bbc;
}

.has-success .form-control {
  border-color: #29b348;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.has-warning .form-control {
  border-color: #f5b225;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.has-error .form-control {
  border-color: #ec536c;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.input-group-addon {
  border-radius: 2px;
  border: 1px solid #ced4da;
}

/* ==============
  Form-Validation
===================*/
/*.error {
  color: #ec536c;
}*/
.error {
      color: red;
    
   }
.parsley-error {
  border-color: #ec536c;
}

.parsley-errors-list {
  display: none;
  margin: 0;
  padding: 0;
}

.parsley-errors-list.filled {
  display: block;
}

.parsley-errors-list > li {
  font-size: 12px;
  list-style: none;
  color: #ec536c;
  margin-top: 5px;
}

/* ==============
  Form-Upload
===================*/
/* Dropzone */
.dropzone {
  min-height: 230px;
  border: 2px dashed rgba(0, 0, 0, 0.3);
  background: #ffffff;
  border-radius: 6px;
}

.dropzone .dz-message {
  font-size: 30px;
}

/* ==============
  Form-Advanced
===================*/
/* Datepicker */
.datepicker {
  border: 1px solid #ced4da;
  padding: 8px;
}

.datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover, .datepicker table tr td.today:hover, .datepicker table tr td.selected,
.datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected:hover {
  background-color: #067bbc !important;
  background-image: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #ffffff;
}

/* Bootstrap-touchSpin */
.bootstrap-touchspin .input-group-btn-vertical .btn {
  padding: 9px 12px;
}

.bootstrap-touchspin .input-group-btn-vertical i {
  top: 4px;
  left: 8px;
}

.input-group-addon {
  padding: .375rem .75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #343a40;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
}

/* Prism */
:not(pre) > code[class*="language-"], pre[class*="language-"] {
  background: #eff3f6;
}

/* Rating */
.badge:empty {
  padding: 0;
}

.select2-container--default .select2-selection--single {
  border: 1px solid #ced4da;
  height: 38px;
}

.select2-container--default .select2-selection--single:focus {
  outline: none;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #343a40;
  line-height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 38px;
  right: 8px;
}
.select2-container .select2-selection--single {
  height: 37px !important;
}

.select2-container .select2-selection--multiple {
  min-height: 38px;
  border: 1px solid #ced4da;
}

.select2-container .select2-search--inline .select2-search__field {
  margin-top: 8px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  margin-top: 7px;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: solid 1px #ced4da;
  outline: 0;
}

/*---datetimepicker---*/
.dtp-btn-cancel {
  margin-right: 5px;
}

.addon-color-picker .btn {
  padding: 8px;
  line-height: 0;
  border-color: #ced4da;
}

/*--colorpicker--*/
.asColorPicker-clear {
  display: none;
  position: absolute;
  top: 8px;
  right: 45px;
  text-decoration: none;
}

.asColorPicker-trigger {
  position: absolute;
  top: 0;
  right: 0;
  height: 38px;
  width: 37px;
  border: 0;
}

.asColorPicker-dropdown {
  max-width: 260px;
}

.asColorPicker-wrap {
  position: relative;
  display: inline-block;
  width: 100%;
  padding-right: 35px;
}

.bootstrap-touchspin-up,
.bootstrap-touchspin-down {
  padding: 8px 14px;
}

/* ==============
  Form Editor
===================*/
.mce-panel {
  border-color: #dfe7ed !important;
  background-color: #eff3f6 !important;
}

.mce-menu-item:hover, .mce-menu-item.mce-selected, .mce-menu-item:focus {
  background-color: #067bbc !important;
}

.mce-menu {
  background-color: #ffffff !important;
}

/* ==============
  Summernote
===================*/
.note-btn-group .dropdown-menu > li > a {
  display: block;
  padding: 5px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #23222f;
  white-space: nowrap;
}

.note-btn-group .dropdown-menu > li > a:hover {
  background-color: #f6f8fa;
}

.note-image-popover, .note-air-popover, .note-link-popover {
  display: none;
}

.note-image-popover .dropdown-toggle::after, .note-air-popover .dropdown-toggle::after, .note-link-popover .dropdown-toggle::after {
  margin-left: 0;
}

.note-icon-caret {
  display: none;
}

.note-editor {
  position: relative;
}

.note-editor .btn-default {
  background-color: transparent;
  border-color: transparent;
}

.note-editor .btn-group-sm > .btn, .note-editor .btn-sm {
  padding: 8px 12px;
}

.note-editor .note-toolbar {
  background-color: #e9ecef;
  border-bottom: 1px solid #eff3f6;
  margin: 0;
}

.note-editor .note-statusbar {
  background-color: #ffffff;
}

.note-editor .note-statusbar .note-resizebar {
  border-top: none;
  height: 15px;
  padding-top: 3px;
}

.note-editor.note-frame {
  border: 1px solid #eff3f6;
}

.note-popover .popover .popover-content {
  padding: 5px 0 10px 5px;
}

.note-popover .btn-default {
  background-color: transparent;
  border-color: transparent;
}

.note-popover .btn-group-sm > .btn, .note-popover .btn-sm {
  padding: 8px 12px;
}

.note-toolbar {
  padding: 5px 0 10px 5px;
}

/* ==============
  Calendar
===================*/
.calendar {
  float: left;
  margin-bottom: 0;
}

.none-border .modal-footer {
  border-top: none;
}

.fc-toolbar {
  margin-bottom: 5px;
}

.fc-toolbar h2 {
  font-size: 18px;
  font-weight: 600;
  line-height: 30px;
  text-transform: uppercase;
}

.fc-day {
  background: #ffffff;
}

.fc-toolbar .fc-state-active, .fc-toolbar .ui-state-active,
.fc-toolbar button:focus, .fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0;
}

.fc-widget-header {
  border: 1px solid #ced4da;
  background-color: #eff3f6;
}

.fc-widget-content {
  border: 1px solid #ced4da;
}

.fc th.fc-widget-header {
  font-size: 14px;
  line-height: 20px;
  padding: 10px 0;
  font-weight: 700;
  text-transform: uppercase;
}

.fc-button {
  background: #ffffff;
  border: 1px solid #ced4da;
  color: #5d5b6f;
  text-transform: capitalize;
}

.fc-text-arrow {
  font-family: arial;
  font-size: 16px;
}

.fc-state-hover {
  background: #5d5b6f;
}

.fc-state-highlight {
  background: #f8f9fa;
}

.fc-cell-overlay {
  background: #f8f9fa;
}

.fc-unthemed .fc-today {
  background: #ffffff;
}

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 13px;
  margin: 5px 0;
  padding: 5px 5px;
  text-align: center;
  background-color: #067bbc;
  color: #ffffff !important;
}

.external-event {
  color: #ffffff;
  cursor: move;
  margin: 10px 0;
  padding: 6px 10px;
}

.fc-basic-view td.fc-week-number span {
  padding-right: 5px;
}

.fc-basic-view td.fc-day-number {
  padding-right: 5px;
}

/* ==============
  Widgets
===================*/
.widget-chart li {
  width: 31.5%;
  display: inline-block;
  padding: 0;
}

.widget-chart li i {
  font-size: 22px;
}

.mini-stat {
  -webkit-box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.05);
          box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.05);
  padding: 20px;
  border-radius: 3px;
  margin-bottom: 30px;
}

.mini-stat-icon {
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 30px;
  border-radius: 100%;
  float: left;
  margin-right: 10px;
}

.mini-stat-info {
  font-size: 14px;
  padding-top: 2px;
}

.mini-stat-info span {
  display: block;
  font-size: 24px;
}

.round {
  line-height: 60px;
  color: #fff;
  width: 60px;
  font-size: 60px;
  display: inline-block;
  font-weight: 400;
/*  border: 1px solid #f8f9fa;*/
  text-align: center;
/*  border-radius: 10%;*/
/*  background: rgba(36, 44, 109, 0.02);*/
}

.jvectormap-zoomin,
.jvectormap-zoomout {
  display: none;
}

.bg-map {
  background-color: #f3f8fb;
  -webkit-box-shadow: none;
          box-shadow: none;
}

#v-cal .vcal-week {
  background-color: #fbfcfc;
}

#v-cal .vcal-week span {
  font-size: 13px !important;
  padding: 14px 8px;
  font-weight: 500;
}

#v-cal .vcal-date {
  padding: 10px;
}

#v-cal .vcal-date--today {
  background-color: #44a2d2;
}

#v-cal .vcal-header svg {
  fill: #44a2d2;
}

/* ==============
  Maps
===================*/
.gmaps, .gmaps-panaroma {
  height: 300px;
  background: #eff3f6;
  border-radius: 3px;
}

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #ffffff;
  font-size: 16px;
  line-height: 40px;
  background: #067bbc;
  border-radius: 4px;
  padding: 10px 20px;
}

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute;
}

.gmaps-overlay_arrow.above {
  bottom: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-top: 16px solid #067bbc;
}

.gmaps-overlay_arrow.below {
  top: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 16px solid #067bbc;
}

/* ==============
  Account Pages
===================*/
.accountbg {
  background: url("../images/bg-account.png");
  background-repeat: repeat-x;
  position: absolute;
  height: 50%;
  width: 100%;
  top: 50%;
}

.wrapper-page {
/*  margin: 7.5% auto;*/
/*  max-width: 460px;*/
  position: relative;
  height: 100vh;
}

.wrapper-page .logo-admin {
  float: none !important;
}

.wrapper-page .card {
/*  border: 7px double #067bbc;*/
/*  border-radius: 10px;*/
}

.user-thumb {
  position: relative;
  z-index: 999;
}

.user-thumb img {
  height: 88px;
  margin: 0 auto;
  width: 88px;
}

.ex-page-content h1 {
  font-size: 98px;
  font-weight: 700;
  line-height: 150px;
  text-shadow: rgba(61, 61, 61, 0.3) 1px 1px, rgba(61, 61, 61, 0.2) 2px 2px, rgba(61, 61, 61, 0.3) 3px 3px;
}

/*
File: Responsive
*/
@media (max-width: 1024px) {
  body {
    overflow-x: hidden;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  body {
    overflow-x: hidden;
  }
}

@media (max-width: 768px) {
  .side-menu.left {
    margin-left: -100%;
  }
  .content-page {
    margin-left: 0;
  }
  .enlarged .side-menu.left {
    margin-left: 0 !important;
  }
  .button-menu-mobile {
    display: inline-block;
  }
  .navbar-custom {
    padding-left: 5px;
  }
  .content-page > .content {
    padding: 20px;
  }
  #wrapper.enlarged .footer, .footer {
    left: 0;
  }
}

@media (max-width: 767px) {
  body {
    overflow-x: hidden;
  }
  .content-page {
    margin-left: 0 !important;
  }
  .enlarged .left.side-menu {
    margin-left: -75px;
  }
  .mobile-sidebar {
    left: 0;
  }
  .mobile-content {
    left: 250px;
    right: -250px;
  }
  .wrapper-page {
    width: 90%;
  }
  .navbar-nav .open .dropdown-menu {
    background-color: #ffffff;
    -webkit-box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
            box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
    left: auto;
    position: absolute;
    right: 0;
  }
  .fc-toolbar .fc-right {
    float: left;
    margin: 10px 0;
  }
}

@media (max-width: 620px) {
  .page-header-title {
    text-align: center;
  }
  .dataTables_paginate .page-link {
    padding: .35rem .5rem;
  }
  .mo-mb-2 {
    margin-bottom: 10px;
  }
  .mo-mt-2 {
    margin-top: 10px !important;
  }
  .pagination-lg .page-link {
    padding: .25rem .5rem;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .side-menu {
    z-index: 10;
  }
  .button-menu-mobile {
    display: block;
  }
  .page-title {
    display: none;
  }
}

@media (max-width: 420px) {
  .hide-phone, .notify-icon {
    display: none;
  }
  .dropdown-menu-lg {
    width: 200px;
  }
  .notify-details {
    margin-left: 0 !important;
  }
}
/*# sourceMappingURL=style.css.map */

.login-form{
  background-image:url('../images/back.png');
  background-repeat: no-repeat;
  background-size: cover;
}
.login-form .form-control{
  padding: 10px;
  height: auto;
  border-radius: 17px;
  border: none;
}
.login-form .form-control:foucs .form-label{
  color: #000;
}
.toggle-password{
    position: absolute;
    right: 10px;
    top: 21px;
    color: #383838e0;
    text-shadow: 0 0 3px #606060;
        font-size: 18px;
}
.hv-100{
  height: 100vh;
}


/*invoice*/
