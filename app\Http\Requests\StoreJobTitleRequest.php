<?php



namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreJobTitleRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'client_id' => 'required|exists:clients,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'nullable|string',
            'posted_date' => 'required|date',
            'slot' => 'required|integer',
            'status' => 'required|string',
        ];
    }
}
