<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAmtOtNdHolidayRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'payroll_id' => 'required|exists:payroll,id',
            'b_ot' => 'nullable|numeric',
            'nsd' => 'nullable|numeric',
            'nsd_ot' => 'nullable|numeric',
            'rdd' => 'nullable|numeric',
            'rdd_ot' => 'nullable|numeric',
            'rdnsd' => 'nullable|numeric',
            'rdnsd_ot' => 'nullable|numeric',
            'sh' => 'nullable|numeric',
            'sh_ot' => 'nullable|numeric',
            'shnsd' => 'nullable|numeric',
            'shnsd_ot' => 'nullable|numeric',
            'lh' => 'nullable|numeric',
            'lh_ot' => 'nullable|numeric',
            'lhnsd' => 'nullable|numeric',
            'lhnsd_ot' => 'nullable|numeric',
            'lhrd' => 'nullable|numeric',
            'lhrd_ot' => 'nullable|numeric',
            'lhrdnsd' => 'nullable|numeric',
            'lhrdnsd_ot' => 'nullable|numeric',
            'shrd' => 'nullable|numeric',
            'shrd_ot' => 'nullable|numeric',
            'shrdnsd' => 'nullable|numeric',
            'shrdnsd_ot' => 'nullable|numeric',
        ];
    }
}
