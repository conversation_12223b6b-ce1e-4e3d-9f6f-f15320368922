import { createBrowserRouter, Navigate } from "react-router-dom";
import Dashboard from "./Dashboard.jsx";
import DefaultLayout from "./components/DefaultLayout";
import GuestLayout from "./components/Guestlayout";
import Login from "./views/Login";
import NotFound from "./views/NotFound";
import Signup from "./views/Signup";
import Users from "./views/Users";
import UserForm from "./views/UserForm";
import ClientForm from "./views/client/ClientForm.jsx";
import Clients from "./views/client/Clients.jsx";
import JobTitleForm from "./views/jobtitle/JobTitleForm.jsx";
import JobTitle from "./views/jobtitle/JobTitle.jsx";
import Applicants from "./views/applicant/Applicants.jsx";
import ApplicantForm from "./views/applicant/ApplicantForm.jsx";
import Contracts from "./views/contracts/Contracts.jsx";
import ContractForm from "./views/contracts/ContractForm.jsx";
import Payroll from "./views/payroll/Payrolls.jsx";
import PayrollForm from "./views/payroll/PayrollForm.jsx";
import ManageEmployees from "./views/employees/ManageEmployees.jsx";

const router = createBrowserRouter([
  {
    path: "/",
    element: <DefaultLayout />,
    children: [
      {
        path: "/",
        element: <Navigate to="/users" />,
      },
      {
        path: "/dashboard",
        element: <Dashboard />,
      },
      {
        path: "/users",
        element: <Users />,
      },
      {
        path: "/users/new",
        element: <UserForm key="userCreate" />,
      },
      {
        path: "/users/:id",
        element: <UserForm key="userUpdate" />,
      },
      //Clients
      {
        path: "/clients",
        element: <Clients />,
      },
      {
        path: "/clients/new",
        element: <ClientForm key="clientCreate" />,
      },
      {
        path: "/clients/:id",
        element: <ClientForm key="clientUpdate" />,
      },
      //Applicants
      {
        path: "/applicants",
        element: <Applicants />,
      },
      {
        path: "/applicants/new",
        element: <ApplicantForm key="applicantCreate" />,
      },
      {
        path: "/applicants/:id",
        element: <ApplicantForm key="applicantUpdate" />,
      },
      //JobTitle
      {
        path: "/jobtitle",
        element: <JobTitle />,
      },
      {
        path: "/jobtitles/new",
        element: <JobTitleForm key="jobtitleCreate" />,
      },
      {
        path: "/jobtitles/:id",
        element: <JobTitleForm key="jobtitleUpdate" />,
      },
      //Contracts
      {
        path: "/contracts",
        element: <Contracts />,
      },
      {
        path: "/contracts/new",
        element: <ContractForm key="contractCreate" />,
      },
      {
        path: "/contracts/:id",
        element: <ContractForm key="contractUpdate" />,
      },
      //Payroll
      {
        path: "/payrolls",
        element: <Payroll />,
      },
      {
        path: "/payrolls/new",
        element: <PayrollForm key="payrollCreate" />,
      },

      //employees
      {
        path: "/manageemployees",
        element: <ManageEmployees />,
      },
    ],
  },
  {
    path: "/",
    element: <GuestLayout />,
    children: [
      {
        path: "/login",
        element: <Login />,
      },
      {
        path: "/signup",
        element: <Signup />,
      },
    ],
  },
  {
    path: "*",
    element: <NotFound />,
  },
]);

export default router;
