import { useEffect, useState } from "react";
import axiosClient from "../../axios-client";
import { Link } from "react-router-dom";
import { useStateContext } from "../../contexts/ContextProvider";
import 'bootstrap/dist/css/bootstrap.min.css'; // Ensure Bootstrap CSS is imported

export default function Payrolls() {
  const [payrolls, setPayrolls] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { setNotification } = useStateContext();

  useEffect(() => {
    getPayrolls();
  }, [currentPage]);

  const getPayrolls = () => {
    setLoading(true);
    axiosClient
      .get(`/payrolls?page=${currentPage}`)
      .then(({ data }) => {
        setLoading(false);
        setPayrolls(data.data);
        setTotalPages(data.meta.last_page);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const goToFirstPage = () => {
    setCurrentPage(1);
  };

  const goToLastPage = () => {
    setCurrentPage(totalPages);
  };

  const getPageNumbers = () => {
    const currentPageIndex = currentPage - 1;
    const maxPagesToShow = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = startPage + maxPagesToShow - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
  };

  return (
    <main className="container mt-5">
      <div className="row mb-3">
        <div className="col-md-6">
          <h1>Manage Payrolls</h1>
        </div>
        <div className="col-md-6 text-end">
          <Link className="btn btn-primary" to="/payrolls/new">
            Add New
          </Link>
        </div>
      </div>
      <div className="card">
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-striped table-bordered">
              <thead className="table-dark">
                <tr>
                  <th>ID</th>
                  <th>Cut Off Date</th>
                  <th>Applicant Name</th>
                  <th>Company Name</th>
                  <th>Designation</th>
                  <th>Net Pay</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="6" className="text-center">
                      Loading...
                    </td>
                  </tr>
                ) : payrolls.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="text-center">
                      No payrolls found
                    </td>
                  </tr>
                ) : (
                  payrolls.map((payroll) => (
                    <tr key={payroll.id}>
                      <td>{payroll.id}</td>
                      <td>{payroll.cut_off_date}</td>
                      <td>
                        {`${payroll.contracts.applicant.last_name}, ${payroll.contracts.applicant.first_name} ${payroll.contracts.applicant.middle_name}`}
                      </td>
                      <td>{payroll.contracts.jobTitle.client.company_name}</td>
                      <td>{payroll.contracts.designation}</td>
                      <td>{payroll.net_pay}</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          {totalPages > 1 && (
            <nav aria-label="Page navigation example">
              <ul className="pagination justify-content-center mt-3">
                <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
                  <button className="page-link" onClick={goToFirstPage} disabled={currentPage === 1}>
                    &laquo;
                  </button>
                </li>
                {getPageNumbers().map((page) => (
                  <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(page)}>
                      {page}
                    </button>
                  </li>
                ))}
                <li className={`page-item ${currentPage === totalPages ? "disabled" : ""}`}>
                  <button className="page-link" onClick={goToLastPage} disabled={currentPage === totalPages}>
                    &raquo;
                  </button>
                </li>
              </ul>
            </nav>
          )}
        </div>
      </div>
    </main>
  );
}
