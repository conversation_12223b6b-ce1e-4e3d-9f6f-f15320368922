<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreatePayrollTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payrolls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contract_id')->constrained('contracts')->onUpdate('no action')->onDelete('no action');
            $table->date('cut_off_date');
            $table->decimal('rw_hrs', 10, 2);
            $table->decimal('rw_day', 10, 2);
            $table->decimal('daily_rate', 10, 2);
            $table->decimal('basic_salary', 10, 2);
            $table->decimal('thirteenth_month_pay', 10, 2);
            $table->decimal('sil', 10, 2);
            $table->decimal('gross_pay', 10, 2);
            $table->decimal('total_deductions', 10, 2);
            $table->decimal('net_pay', 10, 2);
            $table->timestamp('datecreated')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('dateupdated')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payrolls');
    }
}
