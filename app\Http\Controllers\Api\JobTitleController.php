<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\JobTitle;
use App\Http\Requests\StoreJobTitleRequest;
use App\Http\Requests\UpdateJobTitleRequest;
use App\Http\Resources\JobTitleResource;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class JobTitleController extends Controller
{
    public function index()
    {
        return JobTitleResource::collection(JobTitle::query()->orderBy('title', 'desc')->paginate(10));
    }

    public function store(StoreJobTitleRequest $request)
    {
        $jobTitle = JobTitle::create($request->validated());
        return new JobTitleResource($jobTitle);
    }

    public function show($id)
    {
        $jobTitle = JobTitle::find($id);

        if (!$jobTitle) {
            Log::info("Job title with ID $id not found.");
            return response()->json(['error' => 'Job title not found.'], 404);
        }

        Log::info('Fetching job title: ' . $jobTitle->id);
        Log::info('Job title data: ' . json_encode($jobTitle));

        return new JobTitleResource($jobTitle);
    }


    public function update(UpdateJobTitleRequest $request, $id)
{
    try {
        $jobTitle = JobTitle::findOrFail($id);
        $jobTitle->update($request->validated());
        Log::info('Job title updated successfully: ' . json_encode($jobTitle));
        return new JobTitleResource($jobTitle);
    } catch (\Exception $e) {
        Log::error('Error updating job title: ' . $e->getMessage());
        return response()->json(['error' => 'Could not update job title.'], Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}



    public function destroy(JobTitle $jobtitle)
    {
        Log::info('Deleting job title: ' . $jobtitle->id);

        try {
            $jobtitle->delete();
            Log::info('Job title deleted successfully: ' . $jobtitle->id);
            return response()->json(null, Response::HTTP_NO_CONTENT);
        } catch (\Exception $e) {
            Log::error('Error deleting job title: ' . $e->getMessage());
            return response()->json(['error' => 'Could not delete job title.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
