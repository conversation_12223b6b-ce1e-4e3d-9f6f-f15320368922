<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('hours_ot_nd_holiday', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payroll_id')->constrained('payroll')->onDelete('cascade')->onUpdate('cascade');
            $table->decimal('b_ot', 10, 4)->nullable();
            $table->decimal('nsd', 10, 4)->nullable();
            $table->decimal('nsd_ot', 10, 4)->nullable();
            $table->decimal('rdd', 10, 4)->nullable();
            $table->decimal('rdd_ot', 10, 4)->nullable();
            $table->decimal('rdnsd', 10, 4)->nullable();
            $table->decimal('rdnsd_ot', 10, 4)->nullable();
            $table->decimal('sh', 10, 4)->nullable();
            $table->decimal('sh_ot', 10, 4)->nullable();
            $table->decimal('shnsd', 10, 4)->nullable();
            $table->decimal('shnsd_ot', 10, 4)->nullable();
            $table->decimal('lh', 10, 4)->nullable();
            $table->decimal('lh_ot', 10, 4)->nullable();
            $table->decimal('lhnsd', 10, 4)->nullable();
            $table->decimal('lhnsd_ot', 10, 4)->nullable();
            $table->decimal('lhrd', 10, 4)->nullable();
            $table->decimal('lhrd_ot', 10, 4)->nullable();
            $table->decimal('lhrdnsd', 10, 4)->nullable();
            $table->decimal('lhrdnsd_ot', 10, 4)->nullable();
            $table->decimal('shrd', 10, 4)->nullable();
            $table->decimal('shrd_ot', 10, 4)->nullable();
            $table->decimal('shrdnsd', 10, 4)->nullable();
            $table->decimal('shrdnsd_ot', 10, 4)->nullable();
            $table->timestamp('datecreated')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('dateupdated')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
            $table->timestamps();

            $table->index('payroll_id', 'hrs_ibfk');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hours_ot_nd_holiday');
    }
};
