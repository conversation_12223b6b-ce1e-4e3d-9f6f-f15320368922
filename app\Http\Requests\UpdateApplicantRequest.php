<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateApplicantRequest extends FormRequest
{
    public function authorize()
    {
        return true; // You can define authorization logic here if needed
    }

    public function rules()
    {
        $applicantId = $this->route('applicant')->id;

        return [
            'last_name' => 'sometimes|required|string|max:255',
            'first_name' => 'sometimes|required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'email' => 'sometimes|required|email|unique:applicants,email,' . $applicantId,
            'phone' => 'sometimes|required|string|max:20',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:2048', // Adjusted to allow file uploads
            'skills' => 'nullable|string|max:255',
            'highest_educ_attainment' => 'nullable|string|max:255',
            'sex' => 'nullable|string|max:255',
            'age' => 'nullable|integer',
            'status' => 'sometimes|required|string|max:255',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048', // Adjusted to allow file uploads
            'address' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date', // Validates date format
            'place_of_birth' => 'nullable|string|max:255',
            'civil_status' => 'nullable|string|max:255',
            'applied_job' => 'nullable|string|max:255',
        ];
    }
}
