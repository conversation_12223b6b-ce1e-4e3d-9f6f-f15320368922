!function(a,b){"use strict";function c(a,b){for(var c,d=[],g=0;g<a.length;++g){if(c=f[a[g]]||e(a[g]),!c)throw"module definition dependecy not found: "+a[g];d.push(c)}b.apply(null,d)}function d(a,d,e){if("string"!=typeof a)throw"invalid module definition, module id must be defined and be a string";if(d===b)throw"invalid module definition, dependencies must be specified";if(e===b)throw"invalid module definition, definition function must be specified";c(d,function(){f[a]=e.apply(null,arguments)})}function e(b){for(var c=a,d=b.split(/[.\/]/),e=0;e<d.length;++e){if(!c[d[e]])return;c=c[d[e]]}return c}var f={};d("tinymce/tableplugin/Utils",["tinymce/Env"],function(a){function b(a,b){return parseInt(a.getAttribute(b)||1,10)}function c(b){(!a.ie||a.ie>9)&&(b.hasChildNodes()||(b.innerHTML='<br data-mce-bogus="1" />'))}return{getSpanVal:b,paddCell:c}}),d("tinymce/tableplugin/TableGrid",["tinymce/util/Tools","tinymce/Env","tinymce/tableplugin/Utils"],function(a,c,d){var e=a.each,f=d.getSpanVal;return function(g,h,i){function j(){g.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected")}function k(a){return a===g.getBody()}function l(b,c){return b?(c=a.map(c.split(","),function(a){return a.toLowerCase()}),a.grep(b.childNodes,function(b){return-1!==a.inArray(c,b.nodeName.toLowerCase())})):[]}function m(){var a=0;O=[],P=0,e(["thead","tbody","tfoot"],function(b){var c=l(h,b)[0],d=l(c,"tr");e(d,function(c,d){d+=a,e(l(c,"td,th"),function(a,c){var e,g,h,i;if(O[d])for(;O[d][c];)c++;for(h=f(a,"rowspan"),i=f(a,"colspan"),g=d;d+h>g;g++)for(O[g]||(O[g]=[]),e=c;c+i>e;e++)O[g][e]={part:b,real:g==d&&e==c,elm:a,rowspan:h,colspan:i};P=Math.max(P,c+1)})}),a+=d.length})}function n(a){return g.fire("newrow",{node:a}),a}function o(a){return g.fire("newcell",{node:a}),a}function p(a,b){return a=a.cloneNode(b),a.removeAttribute("id"),a}function q(a,b){var c;return c=O[b],c?c[a]:void 0}function r(a,b,c){a&&(c=parseInt(c,10),1===c?a.removeAttribute(b,1):a.setAttribute(b,c,1))}function s(a){return a&&(!!T.getAttrib(a.elm,"data-mce-selected")||a==i)}function t(){var a=[];return e(h.rows,function(b){e(b.cells,function(c){return T.getAttrib(c,"data-mce-selected")||i&&c==i.elm?(a.push(b),!1):void 0})}),a}function u(){var a=T.createRng();k(h)||(a.setStartAfter(h),a.setEndAfter(h),S.setRng(a),T.remove(h))}function v(b){var f,h={};return g.settings.table_clone_elements!==!1&&(h=a.makeMap((g.settings.table_clone_elements||"strong em b i span font h1 h2 h3 h4 h5 h6 p div").toUpperCase(),/[ ,]/)),a.walk(b,function(a){var d;return 3==a.nodeType?(e(T.getParents(a.parentNode,null,b).reverse(),function(a){h[a.nodeName]&&(a=p(a,!1),f?d&&d.appendChild(a):f=d=a,d=a)}),d&&(d.innerHTML=c.ie&&c.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'),!1):void 0},"childNodes"),b=p(b,!1),o(b),r(b,"rowSpan",1),r(b,"colSpan",1),f?b.appendChild(f):d.paddCell(b),b}function w(){var a,b=T.createRng();return e(T.select("tr",h),function(a){0===a.cells.length&&T.remove(a)}),0===T.select("tr",h).length?(b.setStartBefore(h),b.setEndBefore(h),S.setRng(b),void T.remove(h)):(e(T.select("thead,tbody,tfoot",h),function(a){0===a.rows.length&&T.remove(a)}),m(),void(Q&&(a=O[Math.min(O.length-1,Q.y)],a&&(S.select(a[Math.min(a.length-1,Q.x)].elm,!0),S.collapse(!0)))))}function x(a,b,c,d){var e,f,g,h,i;for(e=O[b][a].elm.parentNode,g=1;c>=g;g++)if(e=T.getNext(e,"tr")){for(f=a;f>=0;f--)if(i=O[b+g][f].elm,i.parentNode==e){for(h=1;d>=h;h++)T.insertAfter(v(i),i);break}if(-1==f)for(h=1;d>=h;h++)e.insertBefore(v(e.cells[0]),e.cells[0])}}function y(){e(O,function(a,b){e(a,function(a,c){var d,e,g;if(s(a)&&(a=a.elm,d=f(a,"colspan"),e=f(a,"rowspan"),d>1||e>1)){for(r(a,"rowSpan",1),r(a,"colSpan",1),g=0;d-1>g;g++)T.insertAfter(v(a),a);x(c,b,e-1,d)}})})}function z(b,c,d){var f,g,h,i,j,k,l,n,o,p,t;if(b?(f=J(b),g=f.x,h=f.y,i=g+(c-1),j=h+(d-1)):(Q=R=null,e(O,function(a,b){e(a,function(a,c){s(a)&&(Q||(Q={x:c,y:b}),R={x:c,y:b})})}),Q&&(g=Q.x,h=Q.y,i=R.x,j=R.y)),n=q(g,h),o=q(i,j),n&&o&&n.part==o.part){y(),m(),n=q(g,h).elm;var u=i-g+1,v=j-h+1;for(u===P&&v===O.length&&(u=1,v=1),u===P&&v>1&&(v=1),r(n,"colSpan",u),r(n,"rowSpan",v),l=h;j>=l;l++)for(k=g;i>=k;k++)O[l]&&O[l][k]&&(b=O[l][k].elm,b!=n&&(p=a.grep(b.childNodes),e(p,function(a){n.appendChild(a)}),p.length&&(p=a.grep(n.childNodes),t=0,e(p,function(a){"BR"==a.nodeName&&t++<p.length-1&&n.removeChild(a)})),T.remove(b)));w()}}function A(a){var c,d,g,h,i,j,k,l,m,o;if(e(O,function(b,d){return e(b,function(b){return s(b)&&(b=b.elm,i=b.parentNode,j=n(p(i,!1)),c=d,a)?!1:void 0}),a?!c:void 0}),c!==b){for(h=0,o=0;h<O[0].length;h+=o)if(O[c][h]&&(d=O[c][h].elm,o=f(d,"colspan"),d!=g)){if(a){if(c>0&&O[c-1][h]&&(l=O[c-1][h].elm,m=f(l,"rowSpan"),m>1)){r(l,"rowSpan",m+1);continue}}else if(m=f(d,"rowspan"),m>1){r(d,"rowSpan",m+1);continue}k=v(d),r(k,"colSpan",d.colSpan),j.appendChild(k),g=d}j.hasChildNodes()&&(a?i.parentNode.insertBefore(j,i):T.insertAfter(j,i))}}function B(a){var b,c;e(O,function(c){return e(c,function(c,d){return s(c)&&(b=d,a)?!1:void 0}),a?!b:void 0}),e(O,function(d,e){var g,h,i;d[b]&&(g=d[b].elm,g!=c&&(i=f(g,"colspan"),h=f(g,"rowspan"),1==i?a?(g.parentNode.insertBefore(v(g),g),x(b,e,h-1,i)):(T.insertAfter(v(g),g),x(b,e,h-1,i)):r(g,"colSpan",g.colSpan+1),c=g))})}function C(b){return a.grep(D(b),s)}function D(a){var b=[];return e(a,function(a){e(a,function(a){b.push(a)})}),b}function E(){var b=[];if(k(h)){if(1==O[0].length)return;if(C(O).length==D(O).length)return}e(O,function(c){e(c,function(c,d){s(c)&&-1===a.inArray(b,d)&&(e(O,function(a){var b,c=a[d].elm;b=f(c,"colSpan"),b>1?r(c,"colSpan",b-1):T.remove(c)}),b.push(d))})}),w()}function F(){function a(a){var b,c;e(a.cells,function(a){var c=f(a,"rowSpan");c>1&&(r(a,"rowSpan",c-1),b=J(a),x(b.x,b.y,1,1))}),b=J(a.cells[0]),e(O[b.y],function(a){var b;a=a.elm,a!=c&&(b=f(a,"rowSpan"),1>=b?T.remove(a):r(a,"rowSpan",b-1),c=a)})}var b;b=t(),k(h)&&b.length==h.rows.length||(e(b.reverse(),function(b){a(b)}),w())}function G(){var a=t();if(!k(h)||a.length!=h.rows.length)return T.remove(a),w(),a}function H(){var a=t();return e(a,function(b,c){a[c]=p(b,!0)}),a}function I(b,c){var d,f=t(),g=f[c?0:f.length-1],h=g.cells.length;b&&(d=a.map(b,function(a){return a.cloneNode(!0)}),e(O,function(a){var b;return h=0,e(a,function(a){a.real&&(h+=a.colspan),a.elm.parentNode==g&&(b=1)}),b?!1:void 0}),c||d.reverse(),e(d,function(a){var b,d,e=a.cells.length;for(n(a),b=0;e>b;b++)d=a.cells[b],o(d),r(d,"colSpan",1),r(d,"rowSpan",1);for(b=e;h>b;b++)a.appendChild(o(v(a.cells[e-1])));for(b=h;e>b;b++)T.remove(a.cells[b]);c?g.parentNode.insertBefore(a,g):T.insertAfter(a,g)}),j())}function J(a){var b;return e(O,function(c,d){return e(c,function(c,e){return c.elm==a?(b={x:e,y:d},!1):void 0}),!b}),b}function K(a){Q=J(a)}function L(){var a,b;return a=b=0,e(O,function(c,d){e(c,function(c,e){var f,g;s(c)&&(c=O[d][e],e>a&&(a=e),d>b&&(b=d),c.real&&(f=c.colspan-1,g=c.rowspan-1,f&&e+f>a&&(a=e+f),g&&d+g>b&&(b=d+g)))})}),{x:a,y:b}}function M(a){var b,c,d,e,f,g,h,i,k,l;if(R=J(a),Q&&R){for(b=Math.min(Q.x,R.x),c=Math.min(Q.y,R.y),d=Math.max(Q.x,R.x),e=Math.max(Q.y,R.y),f=d,g=e,l=c;e>=l;l++)for(k=b;d>=k;k++)a=O[l][k],a.real&&(h=a.colspan-1,i=a.rowspan-1,h&&k+h>f&&(f=k+h),i&&l+i>g&&(g=l+i));for(j(),l=c;g>=l;l++)for(k=b;f>=k;k++)O[l][k]&&T.setAttrib(O[l][k].elm,"data-mce-selected","1")}}function N(a,b){var c,d,e;c=J(a),d=c.y*P+c.x;do{if(d+=b,e=q(d%P,Math.floor(d/P)),!e)break;if(e.elm!=a)return S.select(e.elm,!0),T.isEmpty(e.elm)&&S.collapse(!0),!0}while(e.elm==a);return!1}var O,P,Q,R,S=g.selection,T=S.dom;h=h||T.getParent(S.getStart(!0),"table"),m(),i=i||T.getParent(S.getStart(!0),"th,td"),i&&(Q=J(i),R=L(),i=q(Q.x,Q.y)),a.extend(this,{deleteTable:u,split:y,merge:z,insertRow:A,insertCol:B,deleteCols:E,deleteRows:F,cutRows:G,copyRows:H,pasteRows:I,getPos:J,setStartCell:K,setEndCell:M,moveRelIdx:N,refresh:m})}}),d("tinymce/tableplugin/Quirks",["tinymce/util/VK","tinymce/util/Delay","tinymce/Env","tinymce/util/Tools","tinymce/tableplugin/Utils"],function(a,b,c,d,e){var f=d.each,g=e.getSpanVal;return function(h){function i(){function c(c){function d(a,b){var d=a?"previousSibling":"nextSibling",f=h.dom.getParent(b,"tr"),g=f[d];if(g)return r(h,b,g,a),c.preventDefault(),!0;var i=h.dom.getParent(f,"table"),l=f.parentNode,m=l.nodeName.toLowerCase();if("tbody"===m||m===(a?"tfoot":"thead")){var n=e(a,i,l,"tbody");if(null!==n)return j(a,n,b)}return k(a,f,d,i)}function e(a,b,c,d){var e=h.dom.select(">"+d,b),f=e.indexOf(c);if(a&&0===f||!a&&f===e.length-1)return i(a,b);if(-1===f){var g="thead"===c.tagName.toLowerCase()?0:e.length-1;return e[g]}return e[f+(a?-1:1)]}function i(a,b){var c=a?"thead":"tfoot",d=h.dom.select(">"+c,b);return 0!==d.length?d[0]:null}function j(a,b,d){var e=l(b,a);return e&&r(h,d,e,a),c.preventDefault(),!0}function k(a,b,e,f){var g=f[e];if(g)return m(g),!0;var i=h.dom.getParent(f,"td,th");if(i)return d(a,i,c);var j=l(b,!a);return m(j),c.preventDefault(),!1}function l(a,b){var c=a&&a[b?"lastChild":"firstChild"];return c&&"BR"===c.nodeName?h.dom.getParent(c,"td,th"):c}function m(a){h.selection.setCursorLocation(a,0)}function n(){return u==a.UP||u==a.DOWN}function o(a){var b=a.selection.getNode(),c=a.dom.getParent(b,"tr");return null!==c}function p(a){for(var b=0,c=a;c.previousSibling;)c=c.previousSibling,b+=g(c,"colspan");return b}function q(a,b){var c=0,d=0;return f(a.children,function(a,e){return c+=g(a,"colspan"),d=e,c>b?!1:void 0}),d}function r(a,b,c,d){var e=p(h.dom.getParent(b,"td,th")),f=q(c,e),g=c.childNodes[f],i=l(g,d);m(i||g)}function s(a){var b=h.selection.getNode(),c=h.dom.getParent(b,"td,th"),d=h.dom.getParent(a,"td,th");return c&&c!==d&&t(c,d)}function t(a,b){return h.dom.getParent(a,"TABLE")===h.dom.getParent(b,"TABLE")}var u=c.keyCode;if(n()&&o(h)){var v=h.selection.getNode();b.setEditorTimeout(h,function(){s(v)&&d(!c.shiftKey&&u===a.UP,v,c)},0)}}h.on("KeyDown",function(a){c(a)})}function j(){function a(a,b){var c,d=b.ownerDocument,e=d.createRange();return e.setStartBefore(b),e.setEnd(a.endContainer,a.endOffset),c=d.createElement("body"),c.appendChild(e.cloneContents()),0===c.innerHTML.replace(/<(br|img|object|embed|input|textarea)[^>]*>/gi,"-").replace(/<[^>]+>/g,"").length}h.on("KeyDown",function(b){var c,d,e=h.dom;37!=b.keyCode&&38!=b.keyCode||(c=h.selection.getRng(),d=e.getParent(c.startContainer,"table"),d&&h.getBody().firstChild==d&&a(c,d)&&(c=e.createRng(),c.setStartBefore(d),c.setEndBefore(d),h.selection.setRng(c),b.preventDefault()))})}function k(){h.on("KeyDown SetContent VisualAid",function(){var a;for(a=h.getBody().lastChild;a;a=a.previousSibling)if(3==a.nodeType){if(a.nodeValue.length>0)break}else if(1==a.nodeType&&("BR"==a.tagName||!a.getAttribute("data-mce-bogus")))break;a&&"TABLE"==a.nodeName&&(h.settings.forced_root_block?h.dom.add(h.getBody(),h.settings.forced_root_block,h.settings.forced_root_block_attrs,c.ie&&c.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'):h.dom.add(h.getBody(),"br",{"data-mce-bogus":"1"}))}),h.on("PreProcess",function(a){var b=a.node.lastChild;b&&("BR"==b.nodeName||1==b.childNodes.length&&("BR"==b.firstChild.nodeName||"\xa0"==b.firstChild.nodeValue))&&b.previousSibling&&"TABLE"==b.previousSibling.nodeName&&h.dom.remove(b)})}function l(){function a(a,b,c,d){var e,f,g,h=3,i=a.dom.getParent(b.startContainer,"TABLE");return i&&(e=i.parentNode),f=b.startContainer.nodeType==h&&0===b.startOffset&&0===b.endOffset&&d&&("TR"==c.nodeName||c==e),g=("TD"==c.nodeName||"TH"==c.nodeName)&&!d,f||g}function b(){var b=h.selection.getRng(),c=h.selection.getNode(),d=h.dom.getParent(b.startContainer,"TD,TH");if(a(h,b,c,d)){d||(d=c);for(var e=d.lastChild;e.lastChild;)e=e.lastChild;3==e.nodeType&&(b.setEnd(e,e.data.length),h.selection.setRng(b))}}h.on("KeyDown",function(){b()}),h.on("MouseDown",function(a){2!=a.button&&b()})}function m(){function b(a){h.selection.select(a,!0),h.selection.collapse(!0)}function c(a){h.$(a).empty(),e.paddCell(a)}h.on("keydown",function(e){if((e.keyCode==a.DELETE||e.keyCode==a.BACKSPACE)&&!e.isDefaultPrevented()){var f,g,i,j;if(f=h.dom.getParent(h.selection.getStart(),"table")){if(g=h.dom.select("td,th",f),i=d.grep(g,function(a){return!!h.dom.getAttrib(a,"data-mce-selected")}),0===i.length)return j=h.dom.getParent(h.selection.getStart(),"td,th"),void(h.selection.isCollapsed()&&j&&h.dom.isEmpty(j)&&(e.preventDefault(),c(j),b(j)));e.preventDefault(),h.undoManager.transact(function(){g.length==i.length?h.execCommand("mceTableDelete"):(d.each(i,c),b(i[0]))})}}})}m(),c.webkit&&(i(),l()),c.gecko&&(j(),k()),c.ie>9&&(j(),k())}}),d("tinymce/tableplugin/CellSelection",["tinymce/tableplugin/TableGrid","tinymce/dom/TreeWalker","tinymce/util/Tools"],function(a,b,c){return function(d,e){function f(a){d.getBody().style.webkitUserSelect="",(a||o)&&(d.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected"),o=!1)}function g(a,b){return a&&b?a===n.getParent(b,"table"):!1}function h(b){var c,f,h=b.target;if(!m&&h!==l&&(l=h,k&&j)){if(f=n.getParent(h,"td,th"),g(k,f)||(f=n.getParent(k,"td,th")),j===f&&!o)return;if(e(!0),g(k,f)){b.preventDefault(),i||(i=new a(d,k,j),d.getBody().style.webkitUserSelect="none"),i.setEndCell(f),o=!0,c=d.selection.getSel();try{c.removeAllRanges?c.removeAllRanges():c.empty()}catch(p){}}}}var i,j,k,l,m,n=d.dom,o=!0,p=function(){j=i=k=l=null,e(!1)};return d.on("SelectionChange",function(a){o&&a.stopImmediatePropagation()},!0),d.on("MouseDown",function(a){2==a.button||m||(f(),j=n.getParent(a.target,"td,th"),k=n.getParent(j,"table"))}),d.on("mouseover",h),d.on("remove",function(){n.unbind(d.getDoc(),"mouseover",h),f()}),d.on("MouseUp",function(){function a(a,d){var f=new b(a,a);do{if(3==a.nodeType&&0!==c.trim(a.nodeValue).length)return void(d?e.setStart(a,0):e.setEnd(a,a.nodeValue.length));if("BR"==a.nodeName)return void(d?e.setStartBefore(a):e.setEndBefore(a))}while(a=d?f.next():f.prev())}var e,f,g,h,k,l=d.selection;if(j){if(i&&(d.getBody().style.webkitUserSelect=""),f=n.select("td[data-mce-selected],th[data-mce-selected]"),f.length>0){e=n.createRng(),h=f[0],e.setStartBefore(h),e.setEndAfter(h),a(h,1),g=new b(h,n.getParent(f[0],"table"));do if("TD"==h.nodeName||"TH"==h.nodeName){if(!n.getAttrib(h,"data-mce-selected"))break;k=h}while(h=g.next());a(k),l.setRng(e)}d.nodeChanged(),p()}}),d.on("KeyUp Drop SetContent",function(a){f("setcontent"==a.type),p(),m=!1}),d.on("ObjectResizeStart ObjectResized",function(a){m="objectresized"!=a.type}),{clear:f}}}),d("tinymce/tableplugin/Dialogs",["tinymce/util/Tools","tinymce/Env"],function(a,b){var c=a.each;return function(d){function e(){var a=d.settings.color_picker_callback;return a?function(){var b=this;a.call(d,function(a){b.value(a).fire("change")},b.value())}:void 0}function f(a){return{title:"Advanced",type:"form",defaults:{onchange:function(){l(a,this.parents().reverse()[0],"style"==this.name())}},items:[{label:"Style",name:"style",type:"textbox"},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border color",type:"colorbox",name:"borderColor",onaction:e()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:e()}]}]}}function g(a){return a?a.replace(/px$/,""):""}function h(a){return/^[0-9]+$/.test(a)&&(a+="px"),a}function i(a){c("left center right".split(" "),function(b){d.formatter.remove("align"+b,{},a)})}function j(a){c("top middle bottom".split(" "),function(b){d.formatter.remove("valign"+b,{},a)})}function k(b,c,d){function e(b,d){return d=d||[],a.each(b,function(a){var b={text:a.text||a.title};a.menu?b.menu=e(a.menu):(b.value=a.value,c&&c(b)),d.push(b)}),d}return e(b,d||[])}function l(a,b,c){var d=b.toJSON(),e=a.parseStyle(d.style);c?(b.find("#borderColor").value(e["border-color"]||"")[0].fire("change"),b.find("#backgroundColor").value(e["background-color"]||"")[0].fire("change")):(e["border-color"]=d.borderColor,e["background-color"]=d.backgroundColor),b.find("#style").value(a.serializeStyle(a.parseStyle(a.serializeStyle(e))))}function m(a,b,c){var d=a.parseStyle(a.getAttrib(c,"style"));d["border-color"]&&(b.borderColor=d["border-color"]),d["background-color"]&&(b.backgroundColor=d["background-color"]),b.style=a.serializeStyle(d)}function n(a,b,d){var e=a.parseStyle(a.getAttrib(b,"style"));c(d,function(a){e[a.name]=a.value}),a.setAttrib(b,"style",a.serializeStyle(a.parseStyle(a.serializeStyle(e))))}var o=this;o.tableProps=function(){o.table(!0)},o.table=function(e){function j(){function c(a,b,d){if("TD"===a.tagName||"TH"===a.tagName)v.setStyle(a,b,d);else if(a.children)for(var e=0;e<a.children.length;e++)c(a.children[e],b,d)}var e;l(v,this),w=a.extend(w,this.toJSON()),w["class"]===!1&&delete w["class"],d.undoManager.transact(function(){if(p||(p=d.plugins.table.insertTable(w.cols||1,w.rows||1)),d.dom.setAttribs(p,{style:w.style,"class":w["class"]}),d.settings.table_style_by_css){if(u=[],u.push({name:"border",value:w.border}),u.push({name:"border-spacing",value:h(w.cellspacing)}),n(v,p,u),v.setAttribs(p,{"data-mce-border-color":w.borderColor,"data-mce-cell-padding":w.cellpadding,"data-mce-border":w.border}),p.children)for(var a=0;a<p.children.length;a++)c(p.children[a],"border",w.border),c(p.children[a],"padding",h(w.cellpadding))}else d.dom.setAttribs(p,{border:w.border,cellpadding:w.cellpadding,cellspacing:w.cellspacing});v.getAttrib(p,"width")&&!d.settings.table_style_by_css?v.setAttrib(p,"width",g(w.width)):v.setStyle(p,"width",h(w.width)),v.setStyle(p,"height",h(w.height)),e=v.select("caption",p)[0],e&&!w.caption&&v.remove(e),!e&&w.caption&&(e=v.create("caption"),e.innerHTML=b.ie?"\xa0":'<br data-mce-bogus="1"/>',p.insertBefore(e,p.firstChild)),i(p),w.align&&d.formatter.apply("align"+w.align,{},p),d.focus(),d.addVisual()})}function o(a,b){function c(a,c){for(var d=0;d<c.length;d++){var e=v.getStyle(c[d],b);if("undefined"==typeof a&&(a=e),a!=e)return""}return a}var e,f=d.dom.select("td,th",a);return e=c(e,f)}var p,q,r,s,t,u,v=d.dom,w={};e===!0?(p=v.getParent(d.selection.getStart(),"table"),p&&(w={width:g(v.getStyle(p,"width")||v.getAttrib(p,"width")),height:g(v.getStyle(p,"height")||v.getAttrib(p,"height")),cellspacing:g(v.getStyle(p,"border-spacing")||v.getAttrib(p,"cellspacing")),cellpadding:v.getAttrib(p,"data-mce-cell-padding")||v.getAttrib(p,"cellpadding")||o(p,"padding"),border:v.getAttrib(p,"data-mce-border")||v.getAttrib(p,"border")||o(p,"border"),borderColor:v.getAttrib(p,"data-mce-border-color"),caption:!!v.select("caption",p)[0],"class":v.getAttrib(p,"class")},c("left center right".split(" "),function(a){d.formatter.matchNode(p,"align"+a)&&(w.align=a)}))):(q={label:"Cols",name:"cols"},r={label:"Rows",name:"rows"}),d.settings.table_class_list&&(w["class"]&&(w["class"]=w["class"].replace(/\s*mce\-item\-table\s*/g,"")),s={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"table",classes:[a.value]})})})}),t={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:d.settings.table_appearance_options!==!1?[q,r,{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[q,r,{label:"Width",name:"width"},{label:"Height",name:"height"}]},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},s]},d.settings.table_advtab!==!1?(m(v,w,p),d.windowManager.open({title:"Table properties",data:w,bodyType:"tabpanel",body:[{title:"General",type:"form",items:t},f(v)],onsubmit:j})):d.windowManager.open({title:"Table properties",data:w,body:t,onsubmit:j})},o.merge=function(a,b){d.windowManager.open({title:"Merge cells",body:[{label:"Cols",name:"cols",type:"textbox",value:"1",size:10},{label:"Rows",name:"rows",type:"textbox",value:"1",size:10}],onsubmit:function(){var c=this.toJSON();d.undoManager.transact(function(){a.merge(b,c.cols,c.rows)})}})},o.cell=function(){function b(){l(p,this),n=a.extend(n,this.toJSON()),d.undoManager.transact(function(){c(q,function(a){d.dom.setAttribs(a,{scope:n.scope,style:n.style,"class":n["class"]}),d.dom.setStyles(a,{width:h(n.width),height:h(n.height)}),n.type&&a.nodeName.toLowerCase()!=n.type&&(a=p.rename(a,n.type)),i(a),n.align&&d.formatter.apply("align"+n.align,{},a),j(a),n.valign&&d.formatter.apply("valign"+n.valign,{},a)}),d.focus()})}var e,n,o,p=d.dom,q=[];if(q=d.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=d.dom.getParent(d.selection.getStart(),"td,th"),!q.length&&e&&q.push(e),e=e||q[0]){n={width:g(p.getStyle(e,"width")||p.getAttrib(e,"width")),height:g(p.getStyle(e,"height")||p.getAttrib(e,"height")),scope:p.getAttrib(e,"scope"),"class":p.getAttrib(e,"class")},n.type=e.nodeName.toLowerCase(),c("left center right".split(" "),function(a){d.formatter.matchNode(e,"align"+a)&&(n.align=a)}),c("top middle bottom".split(" "),function(a){d.formatter.matchNode(e,"valign"+a)&&(n.valign=a)}),d.settings.table_cell_class_list&&(o={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_cell_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"td",classes:[a.value]})})})});var r={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},o]};d.settings.table_cell_advtab!==!1?(m(p,n,e),d.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:n,body:[{title:"General",type:"form",items:r},f(p)],onsubmit:b})):d.windowManager.open({title:"Cell properties",data:n,body:r,onsubmit:b})}},o.row=function(){function b(){var b,e,f;l(r,this),p=a.extend(p,this.toJSON()),d.undoManager.transact(function(){var a=p.type;c(s,function(c){d.dom.setAttribs(c,{scope:p.scope,style:p.style,"class":p["class"]}),d.dom.setStyles(c,{height:h(p.height)}),a!=c.parentNode.nodeName.toLowerCase()&&(b=r.getParent(c,"table"),e=c.parentNode,f=r.select(a,b)[0],f||(f=r.create(a),b.firstChild?b.insertBefore(f,b.firstChild):b.appendChild(f)),f.appendChild(c),e.hasChildNodes()||r.remove(e)),i(c),p.align&&d.formatter.apply("align"+p.align,{},c)}),d.focus()})}var e,j,n,o,p,q,r=d.dom,s=[];e=d.dom.getParent(d.selection.getStart(),"table"),j=d.dom.getParent(d.selection.getStart(),"td,th"),c(e.rows,function(a){c(a.cells,function(b){return r.getAttrib(b,"data-mce-selected")||b==j?(s.push(a),!1):void 0})}),n=s[0],n&&(p={height:g(r.getStyle(n,"height")||r.getAttrib(n,"height")),scope:r.getAttrib(n,"scope"),"class":r.getAttrib(n,"class")},p.type=n.parentNode.nodeName.toLowerCase(),c("left center right".split(" "),function(a){d.formatter.matchNode(n,"align"+a)&&(p.align=a)}),d.settings.table_row_class_list&&(o={name:"class",type:"listbox",label:"Class",values:k(d.settings.table_row_class_list,function(a){a.value&&(a.textStyle=function(){return d.formatter.getCssText({block:"tr",classes:[a.value]})})})}),q={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"None",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},o]},d.settings.table_row_advtab!==!1?(m(r,p,n),d.windowManager.open({title:"Row properties",data:p,bodyType:"tabpanel",body:[{title:"General",type:"form",items:q},f(r)],onsubmit:b})):d.windowManager.open({title:"Row properties",data:p,body:q,onsubmit:b}))}}}),d("tinymce/tableplugin/ResizeBars",["tinymce/util/Tools","tinymce/util/VK"],function(a,c){var d;return function(e){function f(a,b){return{index:a,y:e.dom.getPos(b).y}}function g(a,b){return{index:a,y:e.dom.getPos(b).y+b.offsetHeight}}function h(a,b){return{index:a,x:e.dom.getPos(b).x}}function i(a,b){return{index:a,x:e.dom.getPos(b).x+b.offsetWidth}}function j(){var a=e.getBody().dir;return"rtl"===a}function k(){return e.inline}function l(){return k?e.getBody().ownerDocument.body:e.getBody()}function m(a,b){return j()?i(a,b):h(a,b)}function n(a,b){return j()?h(a,b):i(a,b)}function o(a,b){return p(a,"width")/p(b,"width")*100}function p(a,b){var c=e.dom.getStyle(a,b,!0),d=parseInt(c,10);return d}function q(a){var b=p(a,"width"),c=p(a.parentElement,"width");return b/c*100}function r(a,b){var c=p(a,"width");return b/c*100}function s(a,b){var c=p(a.parentElement,"width");return b/c*100}function t(a,b,c){for(var d=[],e=1;e<c.length;e++){var f=c[e].element;d.push(a(e-1,f))}var g=c[c.length-1];return d.push(b(c.length-1,g.element)),d}function u(){var b=e.dom.select("."+ma,l());a.each(b,function(a){e.dom.remove(a)})}function v(a){u(),F(a)}function w(a,b,c,d,e,f,g,h){var i={"data-mce-bogus":"all","class":ma+" "+a,unselectable:"on","data-mce-resize":!1,style:"cursor: "+b+"; margin: 0; padding: 0; position: absolute; left: "+c+"px; top: "+d+"px; height: "+e+"px; width: "+f+"px; "};return i[g]=h,i}function x(b,c,d){a.each(b,function(a){var b=d.x,f=a.y-va/2,g=va,h=c;e.dom.add(l(),"div",w(na,oa,b,f,g,h,pa,a.index))})}function y(b,c,d){a.each(b,function(a){var b=a.x-va/2,f=d.y,g=c,h=va;e.dom.add(l(),"div",w(ra,sa,b,f,g,h,ta,a.index))})}function z(b){return a.map(b.rows,function(b){var c=a.map(b.cells,function(a){var b=a.hasAttribute("rowspan")?parseInt(a.getAttribute("rowspan"),10):1,c=a.hasAttribute("colspan")?parseInt(a.getAttribute("colspan"),10):1;return{element:a,rowspan:b,colspan:c}});return{element:b,cells:c}})}function A(c){function d(a,b){return a+","+b}function e(a,b){return h[d(a,b)]}function f(){var b=[];return a.each(i,function(a){b=b.concat(a.cells)}),b}function g(){return i}var h={},i=[],j=0,k=0;return a.each(c,function(c,e){var f=[];a.each(c.cells,function(a){for(var c=0;h[d(e,c)]!==b;)c++;for(var g={element:a.element,colspan:a.colspan,rowspan:a.rowspan,rowIndex:e,colIndex:c},i=0;i<a.colspan;i++)for(var l=0;l<a.rowspan;l++){var m=e+l,n=c+i;h[d(m,n)]=g,j=Math.max(j,m+1),k=Math.max(k,n+1)}f.push(g)}),i.push({element:c.element,cells:f})}),{grid:{maxRows:j,maxCols:k},getAt:e,getAllCells:f,getAllRows:g}}function B(a,b){for(var c=[],d=a;b>d;d++)c.push(d);return c}function C(a,b,c){for(var d,e=a(),f=0;f<e.length;f++)b(e[f])&&(d=e[f]);return d?d:c()}function D(b){var c=B(0,b.grid.maxCols),d=B(0,b.grid.maxRows);return a.map(c,function(a){function c(){for(var c=[],e=0;e<d.length;e++){var f=b.getAt(e,a);f&&f.colIndex===a&&c.push(f)}return c}function e(a){return 1===a.colspan}function f(){for(var c,e=0;e<d.length;e++)if(c=b.getAt(e,a))return c;return null}return C(c,e,f)})}function E(b){var c=B(0,b.grid.maxCols),d=B(0,b.grid.maxRows);return a.map(d,function(a){function d(){for(var d=[],e=0;e<c.length;e++){var f=b.getAt(a,e);f&&f.rowIndex===a&&d.push(f)}return d}function e(a){return 1===a.rowspan}function f(){return b.getAt(a,0)}return C(d,e,f)})}function F(a){var b=z(a),c=A(b),d=E(c),h=D(c),i=e.dom.getPos(a),j=d.length>0?t(f,g,d):[],k=h.length>0?t(m,n,h):[];x(j,a.offsetWidth,i),y(k,a.offsetHeight,i)}function G(a,b,c,d){if(0>b||b>=a.length-1)return"";var e=a[b];if(e)e={value:e,delta:0};else for(var f=a.slice(0,b).reverse(),g=0;g<f.length;g++)f[g]&&(e={value:f[g],delta:g+1});var h=a[b+1];if(h)h={value:h,delta:1};else for(var i=a.slice(b+1),j=0;j<i.length;j++)i[j]&&(h={value:i[j],delta:j+1});var k=h.delta-e.delta,l=Math.abs(h.value-e.value)/k;return c?l/p(d,"width")*100:l}function H(a,b){var c=e.dom.getStyle(a,b);return c||(c=e.dom.getAttrib(a,b)),c||(c=e.dom.getStyle(a,b,!0)),c}function I(a,b,c){var d=H(a,"width"),e=parseInt(d,10),f=b?o(a,c):p(a,"width");return(b&&!R(d)||!b&&!S(d))&&(e=0),!isNaN(e)&&e>0?e:f}function J(b,c,d){for(var e=D(b),f=a.map(e,function(a){return m(a.colIndex,a.element).x}),g=[],h=0;h<e.length;h++){var i=e[h].element.hasAttribute("colspan")?parseInt(e[h].element.getAttribute("colspan"),10):1,j=i>1?G(f,h):I(e[h].element,c,d);j=j?j:wa,g.push(j)}return g}function K(a){var b=H(a,"height"),c=parseInt(b,10);return R(b)&&(c=0),!isNaN(c)&&c>0?c:p(a,"height")}function L(b){for(var c=E(b),d=a.map(c,function(a){return f(a.rowIndex,a.element).y}),e=[],g=0;g<c.length;g++){var h=c[g].element.hasAttribute("rowspan")?parseInt(c[g].element.getAttribute("rowspan"),10):1,i=h>1?G(d,g):K(c[g].element);i=i?i:xa,e.push(i)}return e}function M(b,c,d,e,f){function g(b){return a.map(b,function(){return 0})}function h(){var a;if(f)a=[100-l[0]];else{var b=Math.max(e,l[0]+d);a=[b-l[0]]}return a}function i(a,b){var c,f=g(l.slice(0,a)),h=g(l.slice(b+1));if(d>=0){var i=Math.max(e,l[b]-d);c=f.concat([d,i-l[b]]).concat(h)}else{var j=Math.max(e,l[a]+d),k=l[a]-j;c=f.concat([j-l[a],k]).concat(h)}return c}function j(a,b){var c,f=g(l.slice(0,b));if(d>=0)c=f.concat([d]);else{var h=Math.max(e,l[b]+d);c=f.concat([h-l[b]])}return c}var k,l=b.slice(0);return k=0===b.length?[]:1===b.length?h():0===c?i(0,1):c>0&&c<b.length-1?i(c,c+1):c===b.length-1?j(c-1,c):[]}function N(a,b,c){for(var d=0,e=a;b>e;e++)d+=c[e];return d}function O(b,c){var d=b.getAllCells();return a.map(d,function(a){var b=N(a.colIndex,a.colIndex+a.colspan,c);return{element:a.element,width:b,colspan:a.colspan}})}function P(b,c){var d=b.getAllCells();return a.map(d,function(a){var b=N(a.rowIndex,a.rowIndex+a.rowspan,c);return{element:a.element,height:b,rowspan:a.rowspan}})}function Q(b,c){var d=b.getAllRows();return a.map(d,function(a,b){return{element:a.element,height:c[b]}})}function R(a){return za.test(a)}function S(a){return Aa.test(a)}function T(b,c,d){function f(b,c){a.each(b,function(a){e.dom.setStyle(a.element,"width",a.width+c),e.dom.setAttrib(a.element,"width",null)})}function g(){return d<k.grid.maxCols-1?q(b):q(b)+s(b,c)}function h(){return d<k.grid.maxCols-1?p(b,"width"):p(b,"width")+c}function i(a,c,f){d!=k.grid.maxCols-1&&f||(e.dom.setStyle(b,"width",a+c),e.dom.setAttrib(b,"width",null))}for(var j=z(b),k=A(j),l=R(b.width)||R(b.style.width),m=J(k,l,b),n=l?r(b,c):c,o=M(m,d,n,wa,l,b),t=[],u=0;u<o.length;u++)t.push(o[u]+m[u]);var v=O(k,t),w=l?"%":"px",x=l?g():h();e.undoManager.transact(function(){f(v,w),i(x,w,l)})}function U(b,c,d){for(var f=z(b),g=A(f),h=L(g),i=[],j=0,k=0;k<h.length;k++)i.push(k===d?c+h[k]:h[k]),j+=j[k];var l=P(g,i),m=Q(g,i);e.undoManager.transact(function(){a.each(m,function(a){e.dom.setStyle(a.element,"height",a.height+"px"),e.dom.setAttrib(a.element,"height",null)}),a.each(l,function(a){e.dom.setStyle(a.element,"height",a.height+"px"),e.dom.setAttrib(a.element,"height",null)}),e.dom.setStyle(b,"height",j+"px"),e.dom.setAttrib(b,"height",null)})}function V(){ga=setTimeout(function(){Z()},200)}function W(){clearTimeout(ga)}function X(){var a=document.createElement("div");return a.setAttribute("style","margin: 0; padding: 0; position: fixed; left: 0px; top: 0px; height: 100%; width: 100%;"),a.setAttribute("data-mce-bogus","all"),a}function Y(a,b){e.dom.bind(a,"mouseup",function(){Z()}),e.dom.bind(a,"mousemove",function(a){
W(),ha&&b(a)}),e.dom.bind(a,"mouseout",function(){V()})}function Z(){if(e.dom.remove(ia),ha){e.dom.removeClass(ja,ya),ha=!1;var a,b;if(_(ja)){var c=parseInt(e.dom.getAttrib(ja,ua),10),f=e.dom.getPos(ja).x;a=parseInt(e.dom.getAttrib(ja,ta),10),b=j()?c-f:f-c,T(d,b,a)}else if(aa(ja)){var g=parseInt(e.dom.getAttrib(ja,qa),10),h=e.dom.getPos(ja).y;a=parseInt(e.dom.getAttrib(ja,pa),10),b=h-g,U(d,b,a)}v(d),e.nodeChanged()}}function $(a,b){ia=ia?ia:X(),ha=!0,e.dom.addClass(a,ya),ja=a,Y(ia,b),e.dom.add(l(),ia)}function _(a){return e.dom.hasClass(a,ra)}function aa(a){return e.dom.hasClass(a,na)}function ba(a){ka=ka!==b?ka:a.clientX;var c=a.clientX-ka;ka=a.clientX;var d=e.dom.getPos(ja).x;e.dom.setStyle(ja,"left",d+c+"px")}function ca(a){la=la!==b?la:a.clientY;var c=a.clientY-la;la=a.clientY;var d=e.dom.getPos(ja).y;e.dom.setStyle(ja,"top",d+c+"px")}function da(a){ka=b,$(a,ba)}function ea(a){la=b,$(a,ca)}function fa(a){var b=a.target,c=e.getBody();if(e.$.contains(c,d)||d===c)if(_(b)){a.preventDefault();var f=e.dom.getPos(b).x;e.dom.setAttrib(b,ua,f),da(b)}else if(aa(b)){a.preventDefault();var g=e.dom.getPos(b).y;e.dom.setAttrib(b,qa,g),ea(b)}else u()}var ga,ha,ia,ja,ka,la,ma="mce-resize-bar",na="mce-resize-bar-row",oa="row-resize",pa="data-row",qa="data-initial-top",ra="mce-resize-bar-col",sa="col-resize",ta="data-col",ua="data-initial-left",va=4,wa=10,xa=10,ya="mce-resize-bar-dragging",za=new RegExp(/(\d+(\.\d+)?%)/),Aa=new RegExp(/px|em/);return e.on("init",function(){e.dom.bind(l(),"mousedown",fa)}),e.on("ObjectResized",function(b){var c=b.target;if("TABLE"===c.nodeName){var d=[];a.each(c.rows,function(b){a.each(b.cells,function(a){var b=e.dom.getStyle(a,"width",!0);d.push({cell:a,width:b})})}),a.each(d,function(a){e.dom.setStyle(a.cell,"width",a.width),e.dom.setAttrib(a.cell,"width",null)})}}),e.on("mouseover",function(a){if(!ha){var b=e.dom.getParent(a.target,"table");("TABLE"===a.target.nodeName||b)&&(d=b,v(b))}}),e.on("keydown",function(a){switch(a.keyCode){case c.LEFT:case c.RIGHT:case c.UP:case c.DOWN:u()}}),e.on("remove",function(){u(),e.dom.unbind(l(),"mousedown",fa)}),{adjustWidth:T,adjustHeight:U,clearBars:u,drawBars:F,determineDeltas:M,getTableGrid:A,getTableDetails:z,getWidths:J,getPixelHeights:L,isPercentageBasedSize:R,isPixelBasedSize:S,recalculateWidths:O,recalculateCellHeights:P,recalculateRowHeights:Q}}}),d("tinymce/tableplugin/Plugin",["tinymce/tableplugin/TableGrid","tinymce/tableplugin/Quirks","tinymce/tableplugin/CellSelection","tinymce/tableplugin/Dialogs","tinymce/tableplugin/ResizeBars","tinymce/util/Tools","tinymce/dom/TreeWalker","tinymce/Env","tinymce/PluginManager"],function(a,b,c,d,e,f,g,h,i){function j(f){function g(a){return function(){f.execCommand(a)}}function i(a,b){var c,d,e,g;for(e='<table id="__mce"><tbody>',c=0;b>c;c++){for(e+="<tr>",d=0;a>d;d++)e+="<td>"+(h.ie&&h.ie<10?"&nbsp;":"<br>")+"</td>";e+="</tr>"}return e+="</tbody></table>",f.undoManager.transact(function(){f.insertContent(e),g=f.dom.get("__mce"),f.dom.setAttrib(g,"id",null),f.$("tr",g).each(function(a,b){f.fire("newrow",{node:b}),f.$("th,td",b).each(function(a,b){f.fire("newcell",{node:b})})}),f.dom.setAttribs(g,f.settings.table_default_attributes||{}),f.dom.setStyles(g,f.settings.table_default_styles||{})}),g}function j(a,b,c){function d(){var d,e,g,h={},i=0;e=f.dom.select("td[data-mce-selected],th[data-mce-selected]"),d=e[0],d||(d=f.selection.getStart()),c&&e.length>0?(k(e,function(a){return h[a.parentNode.parentNode.nodeName]=1}),k(h,function(a){i+=a}),g=1!==i):g=!f.dom.getParent(d,b),a.disabled(g),f.selection.selectorChanged(b,function(b){a.disabled(!b)})}f.initialized?d():f.on("init",d)}function l(){j(this,"table")}function m(){j(this,"td,th")}function n(){j(this,"td,th",!0)}function o(){var a="";a='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var b=0;10>b;b++){a+="<tr>";for(var c=0;10>c;c++)a+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*b+c)+'" href="#" data-mce-x="'+c+'" data-mce-y="'+b+'"></a></td>';a+="</tr>"}return a+="</table>",a+='<div class="mce-text-center" role="presentation">1 x 1</div>'}function p(a,b,c){var d,e,g,h,i,j=c.getEl().getElementsByTagName("table")[0],k=c.isRtl()||"tl-tr"==c.parent().rel;for(j.nextSibling.innerHTML=a+1+" x "+(b+1),k&&(a=9-a),e=0;10>e;e++)for(d=0;10>d;d++)h=j.rows[e].childNodes[d].firstChild,i=(k?d>=a:a>=d)&&b>=e,f.dom.toggleClass(h,"mce-active",i),i&&(g=h);return g.parentNode}function q(){f.addButton("tableprops",{title:"Table properties",onclick:y.tableProps,icon:"table"}),f.addButton("tabledelete",{title:"Delete table",onclick:g("mceTableDelete")}),f.addButton("tablecellprops",{title:"Cell properties",onclick:g("mceTableCellProps")}),f.addButton("tablemergecells",{title:"Merge cells",onclick:g("mceTableMergeCells")}),f.addButton("tablesplitcells",{title:"Split cell",onclick:g("mceTableSplitCells")}),f.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:g("mceTableInsertRowBefore")}),f.addButton("tableinsertrowafter",{title:"Insert row after",onclick:g("mceTableInsertRowAfter")}),f.addButton("tabledeleterow",{title:"Delete row",onclick:g("mceTableDeleteRow")}),f.addButton("tablerowprops",{title:"Row properties",onclick:g("mceTableRowProps")}),f.addButton("tablecutrow",{title:"Cut row",onclick:g("mceTableCutRow")}),f.addButton("tablecopyrow",{title:"Copy row",onclick:g("mceTableCopyRow")}),f.addButton("tablepasterowbefore",{title:"Paste row before",onclick:g("mceTablePasteRowBefore")}),f.addButton("tablepasterowafter",{title:"Paste row after",onclick:g("mceTablePasteRowAfter")}),f.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:g("mceTableInsertColBefore")}),f.addButton("tableinsertcolafter",{title:"Insert column after",onclick:g("mceTableInsertColAfter")}),f.addButton("tabledeletecol",{title:"Delete column",onclick:g("mceTableDeleteCol")})}function r(a){var b=f.dom.is(a,"table")&&f.getBody().contains(a);return b}function s(){var a=f.settings.table_toolbar;""!==a&&a!==!1&&(a||(a="tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"),f.addContextToolbar(r,a))}function t(){return v}function u(a){v=a}var v,w,x=this,y=new d(f);!f.settings.object_resizing||f.settings.table_resize_bars===!1||f.settings.object_resizing!==!0&&"table"!==f.settings.object_resizing||(w=e(f)),f.settings.table_grid===!1?f.addMenuItem("inserttable",{text:"Insert table",icon:"table",context:"table",onclick:y.table}):f.addMenuItem("inserttable",{text:"Insert table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(a){a.aria&&(this.parent().hideAll(),a.stopImmediatePropagation(),y.table())},onshow:function(){p(0,0,this.menu.items()[0])},onhide:function(){var a=this.menu.items()[0].getEl().getElementsByTagName("a");f.dom.removeClass(a,"mce-active"),f.dom.addClass(a[0],"mce-active")},menu:[{type:"container",html:o(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(a){var b,c,d=a.target;"A"==d.tagName.toUpperCase()&&(b=parseInt(d.getAttribute("data-mce-x"),10),c=parseInt(d.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"==this.parent().rel)&&(b=9-b),b===this.lastX&&c===this.lastY||(p(b,c,a.control),this.lastX=b,this.lastY=c))},onclick:function(a){var b=this;"A"==a.target.tagName.toUpperCase()&&(a.preventDefault(),a.stopPropagation(),b.parent().cancel(),f.undoManager.transact(function(){i(b.lastX+1,b.lastY+1)}),f.addVisual())}}]}),f.addMenuItem("tableprops",{text:"Table properties",context:"table",onPostRender:l,onclick:y.tableProps}),f.addMenuItem("deletetable",{text:"Delete table",context:"table",onPostRender:l,cmd:"mceTableDelete"}),f.addMenuItem("cell",{separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:g("mceTableCellProps"),onPostRender:m},{text:"Merge cells",onclick:g("mceTableMergeCells"),onPostRender:n},{text:"Split cell",onclick:g("mceTableSplitCells"),onPostRender:m}]}),f.addMenuItem("row",{text:"Row",context:"table",menu:[{text:"Insert row before",onclick:g("mceTableInsertRowBefore"),onPostRender:m},{text:"Insert row after",onclick:g("mceTableInsertRowAfter"),onPostRender:m},{text:"Delete row",onclick:g("mceTableDeleteRow"),onPostRender:m},{text:"Row properties",onclick:g("mceTableRowProps"),onPostRender:m},{text:"-"},{text:"Cut row",onclick:g("mceTableCutRow"),onPostRender:m},{text:"Copy row",onclick:g("mceTableCopyRow"),onPostRender:m},{text:"Paste row before",onclick:g("mceTablePasteRowBefore"),onPostRender:m},{text:"Paste row after",onclick:g("mceTablePasteRowAfter"),onPostRender:m}]}),f.addMenuItem("column",{text:"Column",context:"table",menu:[{text:"Insert column before",onclick:g("mceTableInsertColBefore"),onPostRender:m},{text:"Insert column after",onclick:g("mceTableInsertColAfter"),onPostRender:m},{text:"Delete column",onclick:g("mceTableDeleteCol"),onPostRender:m}]});var z=[];k("inserttable tableprops deletetable | cell row column".split(" "),function(a){"|"==a?z.push({text:"-"}):z.push(f.menuItems[a])}),f.addButton("table",{type:"menubutton",title:"Table",menu:z}),h.isIE||f.on("click",function(a){a=a.target,"TABLE"===a.nodeName&&(f.selection.select(a),f.nodeChanged())}),x.quirks=new b(f),f.on("Init",function(){x.cellSelection=new c(f,function(a){a&&w.clearBars()}),x.resizeBars=w}),f.on("PreInit",function(){f.serializer.addAttributeFilter("data-mce-cell-padding,data-mce-border,data-mce-border-color",function(a,b){for(var c=a.length;c--;)a[c].attr(b,null)})}),k({mceTableSplitCells:function(a){a.split()},mceTableMergeCells:function(a){var b;b=f.dom.getParent(f.selection.getStart(),"th,td"),f.dom.select("td[data-mce-selected],th[data-mce-selected]").length?a.merge():y.merge(a,b)},mceTableInsertRowBefore:function(a){a.insertRow(!0)},mceTableInsertRowAfter:function(a){a.insertRow()},mceTableInsertColBefore:function(a){a.insertCol(!0)},mceTableInsertColAfter:function(a){a.insertCol()},mceTableDeleteCol:function(a){a.deleteCols()},mceTableDeleteRow:function(a){a.deleteRows()},mceTableCutRow:function(a){v=a.cutRows()},mceTableCopyRow:function(a){v=a.copyRows()},mceTablePasteRowBefore:function(a){a.pasteRows(v,!0)},mceTablePasteRowAfter:function(a){a.pasteRows(v)},mceTableDelete:function(a){w&&w.clearBars(),a.deleteTable()}},function(b,c){f.addCommand(c,function(){var c=new a(f);c&&(b(c),f.execCommand("mceRepaint"),x.cellSelection.clear())})}),k({mceInsertTable:y.table,mceTableProps:function(){y.table(!0)},mceTableRowProps:y.row,mceTableCellProps:y.cell},function(a,b){f.addCommand(b,function(b,c){a(c)})}),q(),s(),f.settings.table_tab_navigation!==!1&&f.on("keydown",function(b){var c,d,e;9==b.keyCode&&(c=f.dom.getParent(f.selection.getStart(),"th,td"),c&&(b.preventDefault(),d=new a(f),e=b.shiftKey?-1:1,f.undoManager.transact(function(){!d.moveRelIdx(c,e)&&e>0&&(d.insertRow(),d.refresh(),d.moveRelIdx(c,e))})))}),x.insertTable=i,x.setClipboardRows=u,x.getClipboardRows=t}var k=f.each;i.add("table",j)})}(this);