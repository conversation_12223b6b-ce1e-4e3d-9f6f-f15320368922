<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PayrollDeduction;
use App\Http\Requests\StorePayrollDeductionRequest;
use App\Http\Requests\UpdatePayrollDeductionRequest;
use App\Http\Resources\PayrollDeductionResource;
use Illuminate\Http\Request;

class PayrollDeductionController extends Controller
{
    public function index()
    {
        return PayrollDeductionResource::collection(PayrollDeduction::all());
    }

    public function store(StorePayrollDeductionRequest $request)
    {
        $payrollDeduction = PayrollDeduction::create($request->validated());
        return new PayrollDeductionResource($payrollDeduction);
    }

    public function show(PayrollDeduction $payrollDeduction)
    {
        return new PayrollDeductionResource($payrollDeduction);
    }

    public function update(UpdatePayrollDeductionRequest $request, PayrollDeduction $payrollDeduction)
    {
        $payrollDeduction->update($request->validated());
        return new PayrollDeductionResource($payrollDeduction);
    }

    public function destroy(PayrollDeduction $payrollDeduction)
    {
        $payrollDeduction->delete();
        return response()->noContent();
    }
}
