<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class HoursOtNdHolidayResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'payroll_id' => $this->payroll_id,
            'b_ot' => $this->b_ot,
            'nsd' => $this->nsd,
            'nsd_ot' => $this->nsd_ot,
            'rdd' => $this->rdd,
            'rdd_ot' => $this->rdd_ot,
            'rdnsd' => $this->rdnsd,
            'rdnsd_ot' => $this->rdnsd_ot,
            'sh' => $this->sh,
            'sh_ot' => $this->sh_ot,
            'shnsd' => $this->shnsd,
            'shnsd_ot' => $this->shnsd_ot,
            'lh' => $this->lh,
            'lh_ot' => $this->lh_ot,
            'lhnsd' => $this->lhnsd,
            'lhnsd_ot' => $this->lhnsd_ot,
            'lhrd' => $this->lhrd,
            'lhrd_ot' => $this->lhrd_ot,
            'lhrdnsd' => $this->lhrdnsd,
            'lhrdnsd_ot' => $this->lhrdnsd_ot,
            'shrd' => $this->shrd,
            'shrd_ot' => $this->shrd_ot,
            'shrdnsd' => $this->shrdnsd,
            'shrdnsd_ot' => $this->shrdnsd_ot,
            'datecreated' => $this->datecreated,
            'dateupdated' => $this->dateupdated,
        ];
    }
}
