<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePayrollDeductionRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'payroll_id' => 'required|exists:payroll,id',
            'hrs_late_ut' => 'required|numeric',
            'amt_late_ut' => 'required|numeric',
            'sss_loan' => 'required|numeric',
            'hdmf_loan' => 'required|numeric',
            'uniform_ppe' => 'required|numeric',
            'insurance' => 'required|numeric',
            'other_loans' => 'required|numeric',
            'other_deduc' => 'required|numeric',
        ];
    }
}
