<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PayrollBilling extends Model
{
    use HasFactory;

    protected $fillable = [
        'payroll_id', 'insurance', 'uniform_ppe', 'late_ut', 'gross', 'net_pay',
        'basic_pay', 'payable_ee', 'payable_gov', 'sub_billing', 'admin_fee',
        'admin_subtotal', 'gross_pay_basic', 'add_salaries', 'basic_billing',
        'vat', 'total_billing'
    ];

    public function payroll()
    {
        return $this->belongsTo(Payroll::class);
    }
}
