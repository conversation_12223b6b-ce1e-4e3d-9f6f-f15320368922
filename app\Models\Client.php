<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    use HasFactory;

    // Specify which attributes are mass assignable
    protected $fillable = [
        'company_name',
        'contact_name',
        'email',
        'phone',
        'address',
        'status',
        'logo',
    ];

    // Cast attributes to specific types if needed
    protected $casts = [
        'status' => 'string',
        'email_verified_at' => 'datetime',
    ];
}
