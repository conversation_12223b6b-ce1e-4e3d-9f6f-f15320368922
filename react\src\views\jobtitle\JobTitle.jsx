import { useEffect, useState } from "react";
import axiosClient from "../../axios-client";
import { Link } from "react-router-dom";
import { useStateContext } from "../../contexts/ContextProvider";
import "bootstrap/dist/css/bootstrap.min.css"; // Ensure Bootstrap CSS is imported

export default function JobTitles() {
    const [jobTitles, setJobTitles] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const { setNotification } = useStateContext();

    useEffect(() => {
        getJobTitles();
    }, [currentPage]);

    const onDeleteClick = (jt) => {
        if (
            !window.confirm("Are you sure you want to delete this job title?")
        ) {
            return;
        }

        axiosClient
            .delete(`/jobtitles/${jt.id}`)
            .then(() => {
                setNotification("Job title was successfully deleted");
                getJobTitles();
            })
            .catch((error) => {
                console.error("Error deleting job title:", error);
            });
    };

    const getJobTitles = () => {
        setLoading(true);
        axiosClient
            .get(`/jobtitles?page=${currentPage}`)
            .then(({ data }) => {
                setLoading(false);
                setJobTitles(data.data);
                setTotalPages(data.meta.last_page);
            })
            .catch(() => {
                setLoading(false);
            });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const goToFirstPage = () => {
        setCurrentPage(1);
    };

    const goToLastPage = () => {
        setCurrentPage(totalPages);
    };

    const getPageNumbers = () => {
        const currentPageIndex = currentPage - 1;
        const maxPagesToShow = 5;

        let startPage = Math.max(
            1,
            currentPage - Math.floor(maxPagesToShow / 2)
        );
        let endPage = startPage + maxPagesToShow - 1;

        if (endPage > totalPages) {
            endPage = totalPages;
            startPage = Math.max(1, endPage - maxPagesToShow + 1);
        }

        return Array.from(
            { length: endPage - startPage + 1 },
            (_, index) => startPage + index
        );
    };

    return (
        <main className="container mt-5">
            <div className="row mb-3">
                <div className="col-md-6">
                    <h1>Manage Job Titles</h1>
                </div>
                <div className="col-md-6 text-end">
                    <Link className="btn btn-secondary" to="/jobtitles/new">
                        Add New
                    </Link>
                </div>
            </div>
            <div className="card">
                <div className="card-body">
                    <div className="table-responsive">
                        <table className="table table-striped table-bordered">
                            <thead className="table-dark">
                                <tr>
                                    <th>Title</th>
                                    <th>Description</th>
                                    <th>Requirements</th>
                                    <th>Posted Date</th>
                                    <th>Slot</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {loading ? (
                                    <tr>
                                        <td colSpan="7" className="text-center">
                                            Loading...
                                        </td>
                                    </tr>
                                ) : jobTitles.length === 0 ? (
                                    <tr>
                                        <td colSpan="7" className="text-center">
                                            No job titles found
                                        </td>
                                    </tr>
                                ) : (
                                    jobTitles.map((jt) => (
                                        <tr key={jt.id}>
                                            <td>{jt.title}</td>
                                            <td>{jt.description}</td>
                                            <td>{jt.requirements}</td>
                                            <td>{jt.posted_date}</td>
                                            <td>{jt.slot}</td>
                                            <td>{jt.status}</td>
                                            <td>
                                                <Link
                                                    className="btn btn-info mx-1"
                                                    to={`/jobtitles/${jt.id}`}
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Edit"
                                                >
                                                    <i className="bi bi-file-earmark-text"></i>
                                                </Link>
                                                <button
                                                    className="btn btn-danger mx-1"
                                                    onClick={() =>
                                                        onDeleteClick(jt)
                                                    }
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Delete"
                                                >
                                                    <i className="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                    {totalPages > 1 && (
                        <nav aria-label="Page navigation example">
                            <ul className="pagination justify-content-center mt-3">
                                <li
                                    className={`page-item ${
                                        currentPage === 1 ? "disabled" : ""
                                    }`}
                                >
                                    <button
                                        className="page-link"
                                        onClick={goToFirstPage}
                                        disabled={currentPage === 1}
                                    >
                                        &laquo;
                                    </button>
                                </li>
                                {getPageNumbers().map((page) => (
                                    <li
                                        key={page}
                                        className={`page-item ${
                                            currentPage === page ? "active" : ""
                                        }`}
                                    >
                                        <button
                                            className="page-link"
                                            onClick={() =>
                                                handlePageChange(page)
                                            }
                                        >
                                            {page}
                                        </button>
                                    </li>
                                ))}
                                <li
                                    className={`page-item ${
                                        currentPage === totalPages
                                            ? "disabled"
                                            : ""
                                    }`}
                                >
                                    <button
                                        className="page-link"
                                        onClick={goToLastPage}
                                        disabled={currentPage === totalPages}
                                    >
                                        &raquo;
                                    </button>
                                </li>
                            </ul>
                        </nav>
                    )}
                </div>
            </div>
        </main>
    );
}
