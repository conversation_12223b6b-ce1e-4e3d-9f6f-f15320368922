import { useEffect, useRef, useState } from "react";
import axiosClient from "../../axios-client";
import { Link } from "react-router-dom";
import CustomConfirmModal from "../../components/CustomConfirmModal"; // Import the custom modal

export default function Clients() {
    const [clients, setClients] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [showModal, setShowModal] = useState(false);
    const [clientToDelete, setClientToDelete] = useState(null);
    const [notification, setNotification] = useState("");
    const notificationRef = useRef(null);
    const notificationTimeoutRef = useRef(null);

    useEffect(() => {
        getClients();
    }, [currentPage]);

    useEffect(() => {
        if (notification) {
            if (notificationRef.current) {
                notificationRef.current.scrollIntoView({ behavior: "smooth" });
            }

            // Clear the notification after 5 seconds
            if (notificationTimeoutRef.current) {
                clearTimeout(notificationTimeoutRef.current);
            }
            notificationTimeoutRef.current = setTimeout(() => {
                setNotification("");
            }, 5000);
        }

        // Clean up the timer on component unmount
        return () => {
            if (notificationTimeoutRef.current) {
                clearTimeout(notificationTimeoutRef.current);
            }
        };
    }, [notification]);

    const confirmDelete = () => {
        if (clientToDelete) {
            axiosClient
                .delete(`/clients/${clientToDelete.id}`)
                .then(() => {
                    setNotification("Client was successfully deleted");
                    getClients();
                })
                .catch((error) => {
                    const errorMessage =
                        error.response && error.response.data
                            ? error.response.data.message
                            : "Failed to delete client";
                    if (errorMessage.includes("referenced")) {
                        setNotification(
                            "Cannot delete client because it is referenced in other tables."
                        );
                    } else {
                        setNotification(
                            "An error occurred while deleting the client."
                        );
                    }
                    console.error(
                        "Error deleting client:",
                        error.response ? error.response.data : error.message
                    );
                })
                .finally(() => {
                    setShowModal(false); // Close modal after operation
                    setClientToDelete(null);
                });
        }
    };

    const onDeleteClick = (client) => {
        setClientToDelete(client);
        setShowModal(true);
    };

    const handleModalCancel = () => {
        setShowModal(false);
        setClientToDelete(null);
    };

    const getClients = () => {
        setLoading(true);
        axiosClient
            .get(`/clients?page=${currentPage}`)
            .then(({ data }) => {
                setLoading(false);
                setClients(data.data);
                setTotalPages(data.meta.last_page);
            })
            .catch(() => {
                setLoading(false);
            });
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const goToFirstPage = () => {
        setCurrentPage(1);
    };

    const goToLastPage = () => {
        setCurrentPage(totalPages);
    };

    const getPageNumbers = () => {
        const currentPageIndex = currentPage - 1;
        const maxPagesToShow = 5;

        let startPage = Math.max(
            1,
            currentPage - Math.floor(maxPagesToShow / 2)
        );
        let endPage = startPage + maxPagesToShow - 1;

        if (endPage > totalPages) {
            endPage = totalPages;
            startPage = Math.max(1, endPage - maxPagesToShow + 1);
        }

        return Array.from(
            { length: endPage - startPage + 1 },
            (_, index) => startPage + index
        );
    };

    return (
        <main style={{ backgroundColor: "#EADFB4", padding: "20px" }}>
            {notification && (
                <div className="notification-container" ref={notificationRef}>
                    <div className="alert alert-info">{notification}</div>
                </div>
            )}
            <CustomConfirmModal
                show={showModal}
                message="Are you sure you want to delete this client?"
                onConfirm={confirmDelete}
                onCancel={handleModalCancel}
            />
            <div className="row mb-3">
                <div className="col-md-6">
                    <h1>Manage Clients</h1>
                </div>
                <div className="col-md-6 text-end">
                    <Link className="btn btn-secondary" to="/clients/new">
                        Add New
                    </Link>
                </div>
            </div>

            <div className="container-fluid">
                <div className="row">
                    {loading ? (
                        <div className="col-12 text-center">Loading...</div>
                    ) : clients.length === 0 ? (
                        <div className="col-12 text-center">
                            No clients found
                        </div>
                    ) : (
                        clients.map((c) => (
                            <div className="col-md-4 mb-4" key={c.id}>
                                <div
                                    className="card"
                                    style={{
                                        backgroundColor: "#9BB0C1",
                                        boxShadow:
                                            "0 4px 8px rgba(0, 0, 0, 0.1)",
                                    }}
                                >
                                    <img
                                        src={c.logo || "/default-logo.png"} // Default logo if `c.logo` is null or undefined
                                        alt={`${c.company_name} Logo`}
                                        className="card-img-top profile-logo"
                                        style={{
                                            position: "absolute",
                                            top: "10px",
                                            right: "10px",
                                            height: "100px",
                                            width: "100px",
                                            objectFit: "cover",
                                            borderRadius: "50%", // Circular logo
                                            zIndex: 10, // Ensure the logo is above other content
                                        }}
                                    />

                                    <div className="card-body">
                                        <h5 className="card-title">
                                            {c.company_name}
                                        </h5>
                                        <p className="card-text">
                                            <strong>Email:</strong> {c.email}
                                        </p>
                                        <p className="card-text">
                                            <strong>Status:</strong> {c.status}
                                        </p>
                                        <div className="mt-auto d-flex justify-content-center">
                                            <div
                                                className="btn-group"
                                                role="group"
                                                aria-label="Actions"
                                            >
                                                <Link
                                                    className="btn btn-primary mx-1"
                                                    to={`/clients/${c.id}`}
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Edit"
                                                >
                                                    <i className="bi bi-pencil"></i>
                                                </Link>
                                                <Link
                                                    className="btn btn-info mx-1"
                                                    to={`/payroll/${c.id}`}
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Payroll"
                                                >
                                                    <i className="bi bi-file-earmark-text"></i>
                                                </Link>
                                                <button
                                                    className="btn btn-danger mx-1"
                                                    onClick={() =>
                                                        onDeleteClick(c)
                                                    }
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Delete"
                                                >
                                                    <i className="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))
                    )}
                </div>
                {totalPages > 1 && (
                    <nav aria-label="Page navigation">
                        <ul className="pagination justify-content-center">
                            <li
                                className={`page-item ${
                                    currentPage === 1 ? "disabled" : ""
                                }`}
                            >
                                <button
                                    className="page-link"
                                    onClick={goToFirstPage}
                                    disabled={currentPage === 1}
                                >
                                    &lt;&lt;
                                </button>
                            </li>
                            {getPageNumbers().map((page) => (
                                <li
                                    key={page}
                                    className={`page-item ${
                                        currentPage === page ? "active" : ""
                                    }`}
                                >
                                    <button
                                        className="page-link"
                                        onClick={() => handlePageChange(page)}
                                    >
                                        {page}
                                    </button>
                                </li>
                            ))}
                            <li
                                className={`page-item ${
                                    currentPage === totalPages ? "disabled" : ""
                                }`}
                            >
                                <button
                                    className="page-link"
                                    onClick={goToLastPage}
                                    disabled={currentPage === totalPages}
                                >
                                    &gt;&gt;
                                </button>
                            </li>
                        </ul>
                    </nav>
                )}
            </div>
        </main>
    );
}
