<?php

namespace Database\Factories;

use App\Models\JobTitle;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JobTitle>
 */
class JobTitleFactory extends Factory
{
    protected $model = JobTitle::class;

    public function definition()
    {
        
        return [
            'client_id' => Client::factory(), // Updated syntax for Laravel 8+
            'title' => $this->faker->jobTitle,
            'description' => $this->faker->paragraph,
            'requirements' => $this->faker->text,
            'posted_date' => $this->faker->date,
            'slot' => $this->faker->numberBetween(1, 10),
            'status' => $this->faker->randomElement(['open', 'closed', 'pending']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
