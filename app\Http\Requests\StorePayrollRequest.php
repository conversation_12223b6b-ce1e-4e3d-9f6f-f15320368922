<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePayrollRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'contract_id' => 'required|exists:contracts,id',
            'cut_off_date' => 'required|date',
            'rw_hrs' => 'required|numeric',
            'rw_day' => 'required|numeric',
            'daily_rate' => 'required|numeric',
            'basic_salary' => 'required|numeric',
            'thirteenth_month_pay' => 'required|numeric',
            'sil' => 'required|numeric',
            'gross_pay' => 'required|numeric',
            'total_deductions' => 'required|numeric',
            'net_pay' => 'required|numeric',
            'datecreated' => 'sometimes|required|date',
            'dateupdated' => 'sometimes|required|date',
        ];
    }
}
