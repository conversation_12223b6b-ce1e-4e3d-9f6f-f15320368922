<?php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ContractResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'applicant_id' => $this->applicant_id,
            'jobtitle_id' => $this->jobtitle_id,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'designation' => $this->designation,
            'daily_rate' => $this->daily_rate,
            'emp_status' => $this->emp_status,
            'remarks' => $this->remarks,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'applicant' => new ApplicantResource($this->whenLoaded('applicants')), // Assuming you have a ApplicantResource
            'job_title' => new JobTitleResource($this->whenLoaded('job_titles')), // Assuming you have a JobTitleResource
        ];
    }
}
