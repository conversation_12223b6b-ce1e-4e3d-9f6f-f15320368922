<?php

namespace Database\Factories;

use App\Models\Contract;
use App\Models\Applicant;
use App\Models\JobTitle;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContractFactory extends Factory
{
    protected $model = Contract::class;

    public function definition()
    {
        $startDate = $this->faker->dateTimeBetween('-1 year', 'now');
        $endDate = $this->faker->optional(0.7)->dateTimeBetween($startDate, '+1 year');

        return [
            'applicant_id' => Applicant::inRandomOrder()->value('id') ?: Applicant::factory(),
            'jobtitle_id' => JobTitle::inRandomOrder()->value('id') ?: JobTitle::factory(),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'designation' => $this->faker->jobTitle,
            'daily_rate' => $this->faker->randomFloat(2, 100, 500),
            'emp_status' => $this->faker->randomElement(['Full-time', 'Part-time', 'Contractor']),
            'remarks' => $this->faker->paragraph,
        ];
    }
}


