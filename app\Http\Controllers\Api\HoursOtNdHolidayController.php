<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\HoursOtNdHoliday;
use App\Http\Requests\StoreHoursOtNdHolidayRequest;
use App\Http\Requests\UpdateHoursOtNdHolidayRequest;
use App\Http\Resources\HoursOtNdHolidayResource;
use Illuminate\Http\Request;

class HoursOtNdHolidayController extends Controller
{
    public function index()
    {
        $hours = HoursOtNdHoliday::all();
        return HoursOtNdHolidayResource::collection($hours);
    }

    public function store(StoreHoursOtNdHolidayRequest $request)
    {
        $hours = HoursOtNdHoliday::create($request->validated());
        return new HoursOtNdHolidayResource($hours);
    }

    public function show(HoursOtNdHoliday $hoursOtNdHoliday)
    {
        return new HoursOtNdHolidayResource($hoursOtNdHoliday);
    }

    public function update(UpdateHoursOtNdHolidayRequest $request, HoursOtNdHoliday $hoursOtNdHoliday)
    {
        $hoursOtNdHoliday->update($request->validated());
        return new HoursOtNdHolidayResource($hoursOtNdHoliday);
    }

    public function destroy(HoursOtNdHoliday $hoursOtNdHoliday)
    {
        $hoursOtNdHoliday->delete();
        return response()->noContent();
    }
}
