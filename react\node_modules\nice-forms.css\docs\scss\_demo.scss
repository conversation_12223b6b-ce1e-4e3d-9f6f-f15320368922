@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap");

*,
*:before,
*:after {
  box-sizing: inherit;
}

html {
  font-size: 16px;
  box-sizing: border-box;
}

body {
  background: #f3f0e7;
  font-family: "Roboto", sans-serif;
  color: #4b5563;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.demo-page {
  margin: 0 auto;
  display: flex;
  max-width: 55em;

  .demo-page-navigation {
    width: 18em;
    padding: 2em 1em;

    @media only screen and (max-width: 750px) {
      display: none;
    }

    nav {
      position: -webkit-sticky; /* Safari */
      position: sticky;
      top: 2em;
      background: #ffffff;
      padding: 0.5em;
      border-radius: 0.75rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    a {
      display: flex;
      padding: 0.75em 1em;
      text-decoration: none;
      border-radius: 0.25em;
      color: #374151;
      align-items: center;

      &:hover {
        background: #f3f4f6;
      }

      svg {
        width: 1.25em;
        height: 1.25em;
        margin-right: 1em;
        color: #1f2937;
      }
    }
  }

  .demo-page-content {
    padding: 2em 1em;
    max-width: 100%;

    @media only screen and (min-width: 750px) {
      width: calc(100% - 18em);
    }
  }
}

footer {
  text-align: center;
  margin: 2.5em 0;
}

.href-target {
  position: absolute;
  top: -2em;
}

.to-repo,
.to-reset {
  display: inline-flex;
  background: #24292e;
  color: white;
  border-radius: 5px;
  padding: 0.5em 1em;
  text-decoration: none;
  align-items: center;
  transition: background 0.2s ease-out;

  &:hover {
    background: black;
  }

  svg {
    width: 1.125rem;
    height: 1.125rem;
    margin-right: 0.75em;
  }
}

.to-reset {
  background: #3b4ce2;

  &:hover {
    background: darken(#3b4ce2, 5%);
  }
}

section {
  position: relative;
  background: #ffffff;
  padding: 2em;
  border-radius: 0.75rem;
  line-height: 1.6;
  overflow: hidden;
  margin-bottom: 2rem;
  position: relative;
  font-size: 0.875rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);

  h1 {
    font-weight: 500;
    font-size: 1.25rem;
    color: black;
    margin-bottom: 0.75rem;

    svg {
      width: 1em;
      height: 1em;
      display: inline-block;
      vertical-align: -10%;
      margin-right: 0.25em;
    }

    &.package-name {
      font-size: 2rem;
      margin-bottom: 0.75rem;
      margin-top: -0.5rem;
    }
  }

  strong {
    font-weight: 500;
    color: black;
  }

  p {
    margin: 0.5rem 0 1.5rem;

    a {
      text-decoration: none;
      font-weight: 500;
      color: #3b4ce2;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  code {
    font-weight: 500;
    font-family: Consolas, monaco, monospace;
    position: relative;
    z-index: 1;
    margin: 0 2px;
    background: #f3f4f4;
    content: "";
    border-radius: 3px;
    padding: 2px 5px;
    white-space: nowrap;
  }

  ul {
    margin-top: 0.5em;
    padding-left: 1em;
    list-style-type: disc;
  }
}

details {
  margin-top: 1.5em;
  background: #f1f1f1;
  margin: -2em;
  margin-top: 2em;
  padding: 1.5em 2em;

  .gist {
    margin-top: 1.5em;
  }

  .toggle-code {
    display: inline-block;
    padding: 0.5em 1em;
    border-radius: 5px;
    font-size: 0.875rem;
    background: #10b981;
    top: 1em;
    right: 1em;
    color: white;
    font-weight: 500;
    user-select: none;
    cursor: pointer;

    &:hover {
      background: darken(#10b981, 5%);
    }

    svg {
      display: inline-block;
      vertical-align: -15%;
      margin-right: 0.25em;
    }
  }

  summary {
    outline: none;
    list-style-type: none;

    &::marker {
      display: none;
    }

    &::-webkit-details-marker {
      display: none;
    }
  }
}
