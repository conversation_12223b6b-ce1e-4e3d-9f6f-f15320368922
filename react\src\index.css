/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

* {
    box-sizing: border-box;
}

html, body, #root, #defaultLayout, #guestLayout {
    height: 100%;
    margin: 0;
    overflow: hidden;
}

h1, h2, h3, h4, h5, h6, p {
    margin: 0;
}

body {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    color: #212121;
    background-color: #f6f6f6;
}

input {
    outline: 0;
    background: #ffffff;
    width: 100%;
    border: 2px solid #e6e6e6;
    margin: 0 0 15px;
    padding: 15px;
    font-size: 14px;
    transition: all 0.3s;
}

input:focus {
    border-color: #5b08a7;
}

.btn, .btn-add, .btn-edit, .btn-delete {
    font-family: 'Roboto', sans-serif;
    outline: 0;
    background: #5b08a7;
    border: 0;
    padding: 15px;
    color: #FFFFFF;
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-block {
    width: 100%;
}

.btn-add, .btn-edit, .btn-delete {
    padding: 0.5rem 0.75rem;
    font-size: 14px;
    border-radius: 4px;
}

.btn-add {
    background-color: #00a762;
}

.btn-delete {
    background-color: #b72424;
}

.btn-logout {
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    color: #212121;
    transition: all 0.3s;
    border-radius: 6px;
}

.btn-logout:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.btn:hover, .btn:active, .btn:focus {
    background: #5b08a7;
}

.text-center {
    text-align: center;
}

table {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
}

table > thead > tr > th {
    text-align: left;
    padding: 0.5rem;
    background-color: #efefef;
}

table > tbody > tr > td {
    padding: 0.5rem;
    border-bottom: 1px solid #efefef;
    white-space: nowrap;
}

.table-wrapper {
    overflow-x: auto;
  }

  .sticky-table {
    min-width: 100%; /* Ensures table takes full width */
  }

  .sticky-table th:first-child,
  .sticky-table td:first-child {
    position: sticky;
    left: 0;
    z-index: 1; /* Ensures it stays above other columns */
    background-color: #ffffff; /* Optional: Background color for sticky column */
  }


.card {
    background-color: #FFF;
    border-radius: 0.5rem;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.25rem 1.5rem;
    margin-bottom: 1rem;
    margin-top: 0.5rem;
}

.alert {
    padding: 1rem;
    background-color: #ff4040;
    color: white;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.notification {
    position: fixed;
    right: 1rem;
    bottom: 1rem;
    z-index: 100;
    padding: 1rem 1.5rem;
    background-color: #00a762;
    color: white;
    border-radius: 0.5rem;
}

.login-signup-form {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-signup-form .form {
    width: 360px;
    position: relative;
    z-index: 1;
    background: #FFFFFF;
    max-width: 360px;
    padding: 34px;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
}

.login-signup-form .title {
    font-size: 20px;
    margin-bottom: 1rem;
    text-align: center;
}

.login-signup-form .form .message {
    margin: 15px 0 0;
    color: #b3b3b3;
    font-size: 16px;
    text-align: center;
}

.login-signup-form .form .message a {
    color: #5b08a7;
    text-decoration: none;
}

#defaultLayout {
    display: flex;
    height: 100vh;
}

#defaultLayout aside {
    width: 240px;
    background-color: #1900ff;
    padding: 1rem;
    position: fixed;
    top: 0;
    bottom: 0;
    overflow-y: auto;
}

#defaultLayout aside a {
    color: #ffffff;
    text-decoration: none;
    display: block;
    padding: 0.75rem 1rem;
    transition: color 0.3s ease;
}

#defaultLayout aside a:hover {
    color: #cccccc;
}

#defaultLayout .content {
    margin-left: 240px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

#defaultLayout header {
    height: 80px;
    padding: 2rem 3rem;
    background-color: white;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    left: 240px;
    right: 0;
    z-index: 10;
}

#defaultLayout main {
    padding: 2rem;
    overflow-y: auto;
    flex: 1;
    margin-top: 80px;
}

.animated {
    animation-duration: 0.3s;
    animation-fill-mode: both;
}

.fadeInDown {
    animation-name: fadeInDown;
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
