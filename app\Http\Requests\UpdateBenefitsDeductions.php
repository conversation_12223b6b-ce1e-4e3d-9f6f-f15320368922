<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateBenefitsDeductionsRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'contract_id' => 'sometimes|required|integer',
            'ec_ee_share' => 'sometimes|required|numeric',
            'ec_er_share' => 'sometimes|required|numeric',
            'ec_gov' => 'sometimes|required|numeric',
            'hdmf_ee_share' => 'sometimes|required|numeric',
            'hdmf_er_share' => 'sometimes|required|numeric',
            'hdmf_gov' => 'sometimes|required|numeric',
            'phic_ee_share' => 'sometimes|required|numeric',
            'phic_er_share' => 'sometimes|required|numeric',
            'phic_gov' => 'sometimes|required|numeric',
            'ss_ee_share' => 'sometimes|required|numeric',
            'sss_er_share' => 'sometimes|required|numeric',
            'sss_gov' => 'sometimes|required|numeric',
            'datecreated' => 'nullable|date',
        ];
    }
}
