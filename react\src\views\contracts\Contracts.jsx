import { useEffect, useState } from "react";
import axiosClient from "../../axios-client";
import { Link } from "react-router-dom";
import { useStateContext } from "../../contexts/ContextProvider";
import "bootstrap/dist/css/bootstrap.min.css"; // Ensure Bootstrap CSS is imported

export default function Contracts() {
  const [contracts, setContracts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { setNotification } = useStateContext();

  useEffect(() => {
    getContracts();
  }, [currentPage]);

  const onDeleteClick = (contract) => {
    if (!window.confirm("Are you sure you want to delete this contract?")) {
      return;
    }

    axiosClient
      .delete(`/contracts/${contract.id}`)
      .then(() => {
        setNotification("Contract was successfully deleted");
        getContracts();
      })
      .catch((error) => {
        console.error("Error deleting contract:", error);
      });
  };

  const getContracts = () => {
    setLoading(true);
    axiosClient
      .get(`/contracts?page=${currentPage}`)
      .then(({ data }) => {
        setLoading(false);
        setContracts(data.data);
        setTotalPages(data.meta.last_page);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const goToFirstPage = () => {
    setCurrentPage(1);
  };

  const goToLastPage = () => {
    setCurrentPage(totalPages);
  };

  const getPageNumbers = () => {
    const currentPageIndex = currentPage - 1;
    const maxPagesToShow = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = startPage + maxPagesToShow - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    return Array.from(
      { length: endPage - startPage + 1 },
      (_, index) => startPage + index
    );
  };

  return (
    <main className="container mt-5">
      <div className="row mb-3">
        <div className="col-md-6">
          <h1>Manage Contracts</h1>
        </div>
        <div className="col-md-6 text-end">
          <Link className="btn btn-primary" to="/contracts/new">
            Add New
          </Link>
        </div>
      </div>
      <div className="card">
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-striped table-bordered">
              <thead className="table-dark">
                <tr>
                  <th>Applicant ID</th>
                  <th>Job Title ID</th>
                  <th>Start Date</th>
                  <th>End Date</th>
                  <th>Designation</th>
                  <th>Daily Rate</th>
                  <th>Status</th>
                  <th>Remarks</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="9" className="text-center">
                      Loading...
                    </td>
                  </tr>
                ) : contracts.length === 0 ? (
                  <tr>
                    <td colSpan="9" className="text-center">
                      No contracts found
                    </td>
                  </tr>
                ) : (
                  contracts.map((contract) => (
                    <tr key={contract.id}>
                      <td>{contract.applicant_id}</td>
                      <td>{contract.jobtitle_id}</td>
                      <td>{contract.start_date}</td>
                      <td>{contract.end_date}</td>
                      <td>{contract.designation}</td>
                      <td>{contract.daily_rate}</td>
                      <td>{contract.emp_status}</td>
                      <td>{contract.remarks}</td>
                      <td>
                        <Link
                          className="btn btn-warning btn-sm me-2"
                          to={`/contracts/${contract.id}`}
                        >
                          Edit
                        </Link>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => onDeleteClick(contract)}
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          {totalPages > 1 && (
            <nav aria-label="Page navigation example">
              <ul className="pagination justify-content-center mt-3">
                <li
                  className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
                >
                  <button
                    className="page-link"
                    onClick={goToFirstPage}
                    disabled={currentPage === 1}
                  >
                    &laquo;
                  </button>
                </li>
                {getPageNumbers().map((page) => (
                  <li
                    key={page}
                    className={`page-item ${
                      currentPage === page ? "active" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </button>
                  </li>
                ))}
                <li
                  className={`page-item ${
                    currentPage === totalPages ? "disabled" : ""
                  }`}
                >
                  <button
                    className="page-link"
                    onClick={goToLastPage}
                    disabled={currentPage === totalPages}
                  >
                    &raquo;
                  </button>
                </li>
              </ul>
            </nav>
          )}
        </div>
      </div>
    </main>
  );
}
