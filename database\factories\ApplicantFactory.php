<?php

namespace Database\Factories;

use App\Models\Applicant;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApplicantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Applicant::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'last_name' => $this->faker->lastName,
            'first_name' => $this->faker->firstName,
            'email' => $this->faker->unique()->safeEmail,
            'phone' => $this->faker->phoneNumber,
            'resume' => 'path/to/default/resume.pdf', // Example for default resume path
            'skills' => $this->faker->sentence,
            'highest_educ_attainment' => $this->faker->randomElement(['High School', 'Bachelor\'s Degree', 'Master\'s Degree']),
            'sex' => $this->faker->randomElement(['male', 'female', 'other']),
            'age' => $this->faker->numberBetween(18, 60),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            // Add more fields as needed
        ];
    }
}
