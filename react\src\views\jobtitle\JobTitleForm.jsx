import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import axiosClient from "../../axios-client.js";
import { useStateContext } from "../../contexts/ContextProvider.jsx";

export default function JobTitleForm() {
    const navigate = useNavigate();
    const { id } = useParams();

    const [clients, setClients] = useState([]);
    const [jobTitle, setJobTitle] = useState({
        id: null,
        clientId: "",
        title: "",
        description: "",
        requirements: "",
        postedDate: "",
        slot: "",
        status: "open", // Default value set to "open"
    });

    const [errors, setErrors] = useState(null);
    const [loading, setLoading] = useState(false);
    const { setNotification } = useStateContext();

    useEffect(() => {
        axiosClient
            .get("/clients")
            .then(({ data }) => {
                setClients(data.data); // Assuming data is structured as { data: [{ id, company_name }, ...] }
            })
            .catch((error) => {
                console.error("Error fetching clients:", error);
            });
    }, []);

    useEffect(() => {
        if (id) {
            setLoading(true);
            axiosClient
                .get(`/jobtitles/${id}`)
                .then(({ data }) => {
                    setLoading(false);
                    const jobTitleData = data.data;
                    setJobTitle({
                        id: jobTitleData.id,
                        clientId: jobTitleData.client_id,
                        title: jobTitleData.title,
                        description: jobTitleData.description,
                        requirements: jobTitleData.requirements,
                        postedDate: jobTitleData.posted_date,
                        slot: jobTitleData.slot,
                        status: jobTitleData.status,
                    });

                    // Fetch corresponding client data to get company name
                    axiosClient
                        .get(`/clients/${jobTitleData.client_id}`)
                        .then(({ data }) => {
                            const clientData = data.data;
                            setJobTitle((prevState) => ({
                                ...prevState,
                                clientName: clientData.company_name,
                            }));
                        })
                        .catch((error) => {
                            console.error("Error fetching client data:", error);
                        });
                })
                .catch((error) => {
                    setLoading(false);
                    console.error("Error fetching job title data:", error);
                });
        }
    }, [id]);

    const handleChange = (e, field) => {
        const value = e.target.value.toUpperCase(); // Convert to uppercase
        setJobTitle({ ...jobTitle, [field]: value });
    };

    const onSubmit = (ev) => {
        ev.preventDefault();

        const payload = {
            id: jobTitle.id,
            client_id: jobTitle.clientId,
            title: jobTitle.title,
            description: jobTitle.description,
            requirements: jobTitle.requirements,
            posted_date: jobTitle.postedDate,
            slot: jobTitle.slot,
            status: jobTitle.status,
        };

        if (jobTitle.id) {
            axiosClient
                .put(`/jobtitles/${jobTitle.id}`, payload)
                .then(() => {
                    setNotification("Job Title was successfully updated");
                    navigate("/jobtitle");
                })
                .catch((err) => {
                    const response = err.response;
                    if (response && response.status === 422) {
                        setErrors(response.data.errors);
                    }
                });
        } else {
            axiosClient
                .post("/jobtitles", payload)
                .then(() => {
                    setNotification("Job Title was successfully created");
                    navigate("/jobtitle");
                })
                .catch((err) => {
                    const response = err.response;
                    if (response && response.status === 422) {
                        setErrors(response.data.errors);
                    }
                });
        }
    };

    return (
        <>
            {jobTitle.id ? (
                <h1>Update Job Title: {jobTitle.title}</h1>
            ) : (
                <h1>New Job Title</h1>
            )}
            {loading ? (
                <div className="text-center">Loading...</div>
            ) : (
                <>
                    {errors && (
                        <div className="alert">
                            {Object.keys(errors).map((key) => (
                                <p key={key}>{errors[key][0]}</p>
                            ))}
                        </div>
                    )}
                    <form
                        onSubmit={onSubmit}
                        style={{
                            backgroundColor: "#EADFB4",
                            padding: "20px",
                            borderRadius: "8px",
                        }}
                    >
                        <div className="container-fluid">
                            <div className="row">
                                <div
                                    className="col-lg-6"
                                    style={{ marginLeft: "10%" }}
                                >
                                    <div
                                        className="card"
                                        style={{
                                            backgroundColor: "#9BB0C1",
                                            padding: "20px",
                                            borderRadius: "8px",
                                        }}
                                    >
                                        <div className="card-body">
                                            <div className="tab-content">
                                                <div className="nice-form-group">
                                                    <label htmlFor="client_id">
                                                        Client
                                                    </label>
                                                    {jobTitle.id ? (
                                                        <input
                                                            type="text"
                                                            className="form-control nice-form"
                                                            id="client_id"
                                                            value={
                                                                jobTitle.clientName
                                                            } // Display company name during edit
                                                            readOnly
                                                        />
                                                    ) : (
                                                        <select
                                                            className="form-control nice-form"
                                                            id="client_id"
                                                            value={
                                                                jobTitle.clientId
                                                            }
                                                            onChange={(e) =>
                                                                setJobTitle({
                                                                    ...jobTitle,
                                                                    clientId:
                                                                        e.target
                                                                            .value,
                                                                })
                                                            }
                                                            required
                                                        >
                                                            <option value="">
                                                                Select Client
                                                            </option>
                                                            {clients.map(
                                                                (client) => (
                                                                    <option
                                                                        key={
                                                                            client.id
                                                                        }
                                                                        value={
                                                                            client.id
                                                                        }
                                                                    >
                                                                        {
                                                                            client.company_name
                                                                        }
                                                                    </option>
                                                                )
                                                            )}
                                                        </select>
                                                    )}
                                                </div>

                                                <div className="nice-form-group">
                                                    <label htmlFor="title">
                                                        Title
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control nice-form"
                                                        id="title"
                                                        value={jobTitle.title}
                                                        onChange={(e) =>
                                                            handleChange(
                                                                e,
                                                                "title"
                                                            )
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="description">
                                                        Description
                                                    </label>
                                                    <textarea
                                                        className="form-control nice-form"
                                                        id="description"
                                                        value={
                                                            jobTitle.description
                                                        }
                                                        onChange={(e) =>
                                                            setJobTitle({
                                                                ...jobTitle,
                                                                description:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                        required
                                                    ></textarea>
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="requirements">
                                                        Requirements
                                                    </label>
                                                    <textarea
                                                        className="form-control nice-form"
                                                        id="requirements"
                                                        value={
                                                            jobTitle.requirements
                                                        }
                                                        onChange={(e) =>
                                                            setJobTitle({
                                                                ...jobTitle,
                                                                requirements:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                    ></textarea>
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="posted_date">
                                                        Posted Date
                                                    </label>
                                                    <input
                                                        type="date"
                                                        className="form-control nice-form"
                                                        id="posted_date"
                                                        value={
                                                            jobTitle.postedDate
                                                        }
                                                        onChange={(e) =>
                                                            setJobTitle({
                                                                ...jobTitle,
                                                                postedDate:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="slot">
                                                        Slot
                                                    </label>
                                                    <input
                                                        type="number"
                                                        className="form-control nice-form"
                                                        id="slot"
                                                        value={jobTitle.slot}
                                                        onChange={(e) =>
                                                            setJobTitle({
                                                                ...jobTitle,
                                                                slot: e.target
                                                                    .value,
                                                            })
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="status">
                                                        Status
                                                    </label>
                                                    <select
                                                        className="form-control nice-form"
                                                        id="status"
                                                        value={jobTitle.status}
                                                        onChange={(e) =>
                                                            setJobTitle({
                                                                ...jobTitle,
                                                                status: e.target
                                                                    .value,
                                                            })
                                                        }
                                                    >
                                                        <option value="open">
                                                            Open
                                                        </option>
                                                        <option value="closed">
                                                            Closed
                                                        </option>
                                                    </select>
                                                </div>
                                                <div className="nice-form-group">
                                                    <button
                                                        className="button button2"
                                                        type="submit"
                                                        name="submit"
                                                    >
                                                        Submit
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </>
            )}
        </>
    );
}
