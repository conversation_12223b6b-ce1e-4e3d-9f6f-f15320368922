<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PayrollDeductionResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'payroll_id' => $this->payroll_id,
            'hrs_late_ut' => $this->hrs_late_ut,
            'amt_late_ut' => $this->amt_late_ut,
            'sss_loan' => $this->sss_loan,
            'hdmf_loan' => $this->hdmf_loan,
            'uniform_ppe' => $this->uniform_ppe,
            'insurance' => $this->insurance,
            'other_loans' => $this->other_loans,
            'other_deduc' => $this->other_deduc,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
