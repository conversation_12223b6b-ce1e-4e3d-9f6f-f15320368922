<?php



namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobTitle extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'title',
        'description',
        'requirements',
        'posted_date',
        'slot',
        'status',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }
}

