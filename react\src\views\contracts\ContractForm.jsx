import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import axiosClient from "../../axios-client.js";
import { useStateContext } from "../../contexts/ContextProvider.jsx";

export default function ContractForm() {
    const navigate = useNavigate();
    const { id } = useParams();
    const [activeApplicants, setActiveApplicants] = useState([]);
    const [jobTitles, setJobTitles] = useState([]);
    const [contract, setContract] = useState({
        id: null,
        applicantId: "",
        jobtitleId: "",
        startDate: "",
        endDate: "",
        designation: "",
        dailyRate: "",
        empStatus: "",
        remarks: "",
    });
    const [applicantName, setApplicantName] = useState("");
    const [jobTitleName, setJobTitleName] = useState("");
    const [errors, setErrors] = useState(null);
    const [loading, setLoading] = useState(false);
    const { setNotification } = useStateContext();
    const [searchQuery, setSearchQuery] = useState("");

    useEffect(() => {
        // Fetch all applicants and filter by status 'Active'
        axiosClient
            .get("/applicants")
            .then(({ data }) => {
                // Assuming the API returns an array of applicants
                setActiveApplicants(data.data); // Update state with active applicants
            })
            .catch((error) => {
                console.error("Error fetching applicants:", error);
            });

        // Fetch job titles
        axiosClient
            .get("/jobtitles")
            .then(({ data }) => {
                setJobTitles(data.data);
            })
            .catch((error) => {
                console.error("Error fetching job titles:", error);
            });
    }, []);

    useEffect(() => {
        if (id) {
            setLoading(true);
            axiosClient
                .get(`/contracts/${id}`)
                .then(({ data }) => {
                    setLoading(false);
                    const contractData = data.data;
                    setContract({
                        id: contractData.id,
                        applicantId: contractData.applicant_id,
                        jobtitleId: contractData.jobtitle_id,
                        startDate: contractData.start_date,
                        endDate: contractData.end_date,
                        designation: contractData.designation,
                        dailyRate: contractData.daily_rate,
                        empStatus: contractData.emp_status,
                        remarks: contractData.remarks,
                    });

                    // Find the specific applicant from the filtered list
                    const applicant = activeApplicants.find(
                        (app) => app.id === contractData.applicant_id
                    );
                    if (applicant) {
                        setApplicantName(
                            `${applicant.first_name} ${applicant.last_name}`
                        );
                    }

                    // Find the specific job title
                    axiosClient
                        .get(`/jobtitles/${contractData.jobtitle_id}`)
                        .then(({ data }) => {
                            const jobTitleData = data.data;
                            setJobTitleName(jobTitleData.title);
                        })
                        .catch((error) => {
                            console.error(
                                "Error fetching job title data:",
                                error
                            );
                        });
                })
                .catch((error) => {
                    setLoading(false);
                    console.error("Error fetching contract data:", error);
                });
        }
    }, [id, activeApplicants]); // Ensure that activeApplicants is included as a dependency

    const onSubmit = (ev) => {
        ev.preventDefault();

        const payload = {
            id: contract.id,
            applicant_id: contract.applicantId,
            jobtitle_id: contract.jobtitleId,
            start_date: contract.startDate,
            end_date: contract.endDate,
            designation: contract.designation,
            daily_rate: contract.dailyRate,
            emp_status: contract.empStatus,
            remarks: contract.remarks,
        };

        if (contract.id) {
            axiosClient
                .put(`/contracts/${contract.id}`, payload)
                .then(() => {
                    setNotification("Contract was successfully updated");
                    navigate("/contracts");
                })
                .catch((err) => {
                    const response = err.response;
                    if (response && response.status === 422) {
                        setErrors(response.data.errors);
                    }
                });
        } else {
            axiosClient
                .post("/contracts", payload)
                .then(() => {
                    setNotification("Contract was successfully created");
                    navigate("/contracts");
                })
                .catch((err) => {
                    const response = err.response;
                    if (response && response.status === 422) {
                        setErrors(response.data.errors);
                    }
                });
        }
    };

    // Define the handleSearch function
    const handleSearch = (event) => {
        setSearchQuery(event.target.value);
    };

    // Filter applicants based on search query
    const filteredApplicants = activeApplicants.filter((applicant) =>
        `${applicant.first_name} ${applicant.last_name}`
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
    );

    return (
        <>
            {contract.id ? <h1>Update Contract</h1> : <h1>New Contract</h1>}
            {loading ? (
                <div className="text-center">Loading...</div>
            ) : (
                <>
                    {errors && (
                        <div className="alert">
                            {Object.keys(errors).map((key) => (
                                <p key={key}>{errors[key][0]}</p>
                            ))}
                        </div>
                    )}
                    <form
                        onSubmit={onSubmit}
                        style={{
                            backgroundColor: "#EADFB4",
                            padding: "20px",
                            borderRadius: "8px",
                        }}
                    >
                        <div className="container-fluid">
                            <div className="row">
                                <div
                                    className="col-lg-6"
                                    style={{ marginLeft: "10%" }}
                                >
                                    <div
                                        className="card"
                                        style={{
                                            backgroundColor: "#9BB0C1",
                                            padding: "20px",
                                            borderRadius: "8px",
                                        }}
                                    >
                                        <div className="card-body">
                                            <div className="tab-content">
                                                <div className="nice-form-group">
                                                    <label htmlFor="search">
                                                        Search Applicants
                                                    </label>
                                                    <input
                                                        type="text"
                                                        id="search"
                                                        className="form-control nice-form"
                                                        placeholder="Search by name"
                                                        value={searchQuery}
                                                        onChange={handleSearch}
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="applicant_id">
                                                        Applicant
                                                    </label>
                                                    {contract.id ? (
                                                        <input
                                                            type="text"
                                                            className="form-control nice-form"
                                                            id="applicant_id"
                                                            value={
                                                                applicantName
                                                            } // Display applicant name during edit
                                                            readOnly
                                                        />
                                                    ) : (
                                                        <select
                                                            className="form-control nice-form"
                                                            id="applicant_id"
                                                            value={
                                                                contract.applicantId
                                                            }
                                                            onChange={(e) =>
                                                                setContract({
                                                                    ...contract,
                                                                    applicantId:
                                                                        e.target
                                                                            .value,
                                                                })
                                                            }
                                                            required
                                                        >
                                                            <option value="">
                                                                Select Applicant
                                                            </option>
                                                            {filteredApplicants.map(
                                                                (applicant) => (
                                                                    <option
                                                                        key={
                                                                            applicant.id
                                                                        }
                                                                        value={
                                                                            applicant.id
                                                                        }
                                                                    >
                                                                        {
                                                                            applicant.first_name
                                                                        }{" "}
                                                                        {
                                                                            applicant.last_name
                                                                        }
                                                                    </option>
                                                                )
                                                            )}
                                                        </select>
                                                    )}
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="jobtitle_id">
                                                        Job Title
                                                    </label>
                                                    {contract.id ? (
                                                        <input
                                                            type="text"
                                                            className="form-control nice-form"
                                                            id="jobtitle_id"
                                                            value={jobTitleName} // Display job title name during edit
                                                            readOnly
                                                        />
                                                    ) : (
                                                        <select
                                                            className="form-control nice-form"
                                                            id="jobtitle_id"
                                                            value={
                                                                contract.jobtitleId
                                                            }
                                                            onChange={(e) =>
                                                                setContract({
                                                                    ...contract,
                                                                    jobtitleId:
                                                                        e.target
                                                                            .value,
                                                                })
                                                            }
                                                            required
                                                        >
                                                            <option value="">
                                                                Select Job Title
                                                            </option>
                                                            {jobTitles.map(
                                                                (jobTitle) => (
                                                                    <option
                                                                        key={
                                                                            jobTitle.id
                                                                        }
                                                                        value={
                                                                            jobTitle.id
                                                                        }
                                                                    >
                                                                        {
                                                                            jobTitle.title
                                                                        }
                                                                    </option>
                                                                )
                                                            )}
                                                        </select>
                                                    )}
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="start_date">
                                                        Start Date
                                                    </label>
                                                    <input
                                                        type="date"
                                                        className="form-control nice-form"
                                                        id="start_date"
                                                        value={
                                                            contract.startDate
                                                        }
                                                        onChange={(e) =>
                                                            setContract({
                                                                ...contract,
                                                                startDate:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="end_date">
                                                        End Date
                                                    </label>
                                                    <input
                                                        type="date"
                                                        className="form-control nice-form"
                                                        id="end_date"
                                                        value={contract.endDate}
                                                        onChange={(e) =>
                                                            setContract({
                                                                ...contract,
                                                                endDate:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="designation">
                                                        Designation
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control nice-form"
                                                        id="designation"
                                                        value={
                                                            contract.designation
                                                        }
                                                        onChange={(e) =>
                                                            setContract({
                                                                ...contract,
                                                                designation:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="daily_rate">
                                                        Daily Rate
                                                    </label>
                                                    <input
                                                        type="number"
                                                        className="form-control nice-form"
                                                        id="daily_rate"
                                                        value={
                                                            contract.dailyRate
                                                        }
                                                        onChange={(e) =>
                                                            setContract({
                                                                ...contract,
                                                                dailyRate:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="emp_status">
                                                        Status
                                                    </label>
                                                    <select
                                                        className="form-control nice-form"
                                                        id="emp_status"
                                                        value={
                                                            contract.empStatus
                                                        }
                                                        onChange={(e) =>
                                                            setContract({
                                                                ...contract,
                                                                empStatus:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                    >
                                                        <option value="active">
                                                            Active
                                                        </option>
                                                        <option value="inactive">
                                                            Inactive
                                                        </option>
                                                    </select>
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="remarks">
                                                        Remarks
                                                    </label>
                                                    <textarea
                                                        className="form-control nice-form"
                                                        id="remarks"
                                                        value={contract.remarks}
                                                        onChange={(e) =>
                                                            setContract({
                                                                ...contract,
                                                                remarks:
                                                                    e.target
                                                                        .value,
                                                            })
                                                        }
                                                    ></textarea>
                                                </div>
                                                <div className="nice-form-group">
                                                    <button
                                                        className="button button2"
                                                        type="submit"
                                                        name="submit"
                                                    >
                                                        Submit
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </>
            )}
        </>
    );
}
