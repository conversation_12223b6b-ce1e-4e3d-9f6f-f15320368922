<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Contract;
use Illuminate\Http\Request;
use App\Http\Resources\ContractResource;
use App\Http\Requests\StoreContractRequest;
use App\Http\Requests\UpdateContractRequest;

class ContractController extends Controller
{
    public function index()
    {

        return ContractResource::collection(Contract::query()->orderBy('id', 'desc')->paginate(10));
    }

    public function store(StoreContractRequest $request)
    {
        $validated = $request->validated();

        $contract = Contract::create($validated);

        return new ContractResource($contract);
    }

    public function show(Contract $contract)
    {
        return new ContractResource($contract);
    }

    public function update(UpdateContractRequest $request, Contract $contract)
    {
        $validated = $request->validated();

        $contract->update($validated);

        return new ContractResource($contract);
    }

    public function destroy(Contract $contract)
    {
        $contract->delete();

        return response()->json(null, 204);
    }
};
