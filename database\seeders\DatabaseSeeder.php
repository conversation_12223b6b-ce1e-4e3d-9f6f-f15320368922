<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Client;
use App\Models\JobTitle;
use App\Models\Applicant;
use App\Models\Contract;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        // Disable foreign key checks to avoid constraint violations
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Truncate the tables
        Client::truncate();
        JobTitle::truncate();
        Applicant::truncate();
        Contract::truncate();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

      

        // Define the number of applicants you want to seed
        
        $clientCount = 20;
        $jobtitletCount = 50;
        $applicantCount = 100;
        $contractCount = 100;

        // Using the factory to create applicants and seed the database
        
        Client::factory()->count($clientCount)->create();
        JobTitle::factory()->count($jobtitletCount)->create();
        Applicant::factory()->count($applicantCount)->create();
        Contract::factory()->count($contractCount)->create();
    }
}

