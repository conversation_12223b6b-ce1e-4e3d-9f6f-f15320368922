import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import axiosClient from "../../axios-client.js";
import { useStateContext } from "../../contexts/ContextProvider.jsx";

export default function ClientForm() {
    const navigate = useNavigate();
    const { id } = useParams();

    const [client, setClient] = useState({
        id: null,
        companyName: "",
        contactName: "",
        email: "",
        phone: "",
        address: "",
        status: "",
        logoFile: "",
    });

    const [errors, setErrors] = useState(null);
    const [loading, setLoading] = useState(false);
    const { setNotification } = useStateContext();

    useEffect(() => {
        if (id) {
            setLoading(true);
            axiosClient
                .get(`/clients/${id}`)
                .then(({ data }) => {
                    setLoading(false);
                    const clientData = data.data;
                    setClient({
                        id: clientData.id,
                        companyName: clientData.company_name,
                        contactName: clientData.contact_name,
                        email: clientData.email,
                        phone: clientData.phone,
                        address: clientData.address,
                        status: clientData.status,
                        logoFile: clientData.logo, // This should be handled differently if you need to display the file
                    });
                    console.log("Fetched data:", clientData); // Ensure data is fetched correctly
                })
                .catch((error) => {
                    setLoading(false);
                    console.error("Error fetching client data:", error);
                });
        }
    }, [id]);

    const handleChange = (e, field) => {
        const value =
            field === "email" || field === "address"
                ? e.target.value
                : e.target.value.toUpperCase();
        setClient({ ...client, [field]: value });
    };

    const onSubmit = (ev) => {
        ev.preventDefault();

        const formData = new FormData();
        formData.append("company_name", client.companyName);
        formData.append("contact_name", client.contactName);
        formData.append("email", client.email);
        formData.append("phone", client.phone);
        formData.append("address", client.address);
        formData.append("status", client.status);
        formData.append("client_id", client.id); // Include client_id for file renaming
        if (client.logoFile) {
            formData.append("logo", client.logoFile);
        }

        if (client.id) {
            axiosClient
                .put(`/clients/${client.id}`, formData, {
                    headers: { "Content-Type": "multipart/form-data" },
                })
                .then(() => {
                    setNotification("Client was successfully updated");
                    navigate("/clients");
                })
                .catch((err) => {
                    const response = err.response;
                    if (response && response.status === 422) {
                        setErrors(response.data.errors);
                    }
                });
        } else {
            axiosClient
                .post("/clients", formData, {
                    headers: { "Content-Type": "multipart/form-data" },
                })
                .then(() => {
                    setNotification("Client was successfully created");
                    navigate("/clients");
                })
                .catch((err) => {
                    const response = err.response;
                    if (response && response.status === 422) {
                        setErrors(response.data.errors);
                    }
                });
        }
    };

    return (
        <>
            {client.id ? (
                <h1>Update Client: {client.companyName}</h1>
            ) : (
                <h1>New Client</h1>
            )}
            {loading ? (
                <div className="text-center">Loading...</div>
            ) : (
                <>
                    {errors && (
                        <div className="alert">
                            {Object.keys(errors).map((key) => (
                                <p key={key}>{errors[key][0]}</p>
                            ))}
                        </div>
                    )}
                    <form
                        onSubmit={onSubmit}
                        style={{
                            backgroundColor: "#EADFB4",
                            padding: "20px",
                            borderRadius: "8px",
                        }}
                    >
                        <div className="container-fluid">
                            <div className="row">
                                <div
                                    className="col-lg-6"
                                    style={{ marginLeft: "10%" }}
                                >
                                    <div
                                        className="card"
                                        style={{
                                            backgroundColor: "#9BB0C1",
                                            padding: "20px",
                                            borderRadius: "8px",
                                        }}
                                    >
                                        <div className="card-body">
                                            <div className="tab-content">
                                                <div className="nice-form-group">
                                                    <label htmlFor="company_name">
                                                        Company Name
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control nice-form"
                                                        id="company_name"
                                                        value={
                                                            client.companyName
                                                        }
                                                        onChange={(e) =>
                                                            handleChange(
                                                                e,
                                                                "companyName"
                                                            )
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="contact_name">
                                                        Contact Name
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control nice-form"
                                                        id="contact_name"
                                                        value={
                                                            client.contactName
                                                        }
                                                        onChange={(e) =>
                                                            handleChange(
                                                                e,
                                                                "contactName"
                                                            )
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="email">
                                                        Email
                                                    </label>
                                                    <input
                                                        type="email"
                                                        className="form-control nice-form"
                                                        id="email"
                                                        value={client.email}
                                                        onChange={(e) =>
                                                            handleChange(
                                                                e,
                                                                "email"
                                                            )
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="phone">
                                                        Phone
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control nice-form"
                                                        id="phone"
                                                        value={client.phone}
                                                        onChange={(e) =>
                                                            handleChange(
                                                                e,
                                                                "phone"
                                                            )
                                                        }
                                                        required
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="address">
                                                        Address
                                                    </label>
                                                    <textarea
                                                        className="form-control nice-form"
                                                        id="address"
                                                        value={client.address}
                                                        onChange={(e) =>
                                                            handleChange(
                                                                e,
                                                                "address"
                                                            )
                                                        }
                                                        required
                                                    ></textarea>
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="status">
                                                        Status
                                                    </label>
                                                    <select
                                                        className="form-control nice-form"
                                                        id="status"
                                                        value={client.status}
                                                        onChange={(e) =>
                                                            handleChange(
                                                                e,
                                                                "status"
                                                            )
                                                        }
                                                    >
                                                        <option value="active">
                                                            Active
                                                        </option>
                                                        <option value="inactive">
                                                            Inactive
                                                        </option>
                                                    </select>
                                                </div>
                                                <div className="nice-form-group">
                                                    <label htmlFor="logo">
                                                        Company Logo
                                                    </label>
                                                    <input
                                                        type="file"
                                                        className="form-control-file nice-form"
                                                        id="logo"
                                                        onChange={(e) =>
                                                            setClient({
                                                                ...client,
                                                                logoFile:
                                                                    e.target
                                                                        .files[0],
                                                            })
                                                        }
                                                        accept="image/*"
                                                    />
                                                </div>
                                                <div className="nice-form-group">
                                                    <button
                                                        className="button button2"
                                                        type="submit"
                                                        name="submit"
                                                    >
                                                        Submit
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </>
            )}
        </>
    );
}
