import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import axiosClient from "../../axios-client.js";
import { useStateContext } from "../../contexts/ContextProvider.jsx";

export default function ApplicantForm() {
  const navigate = useNavigate();
  const { id } = useParams();

  const [applicant, setApplicant] = useState({
    id: null,
    lastName: "",
    firstName: "",
    middleName: "",
    email: "",
    phone: "",
    skills: "",
    highestEducationAttainment: "",
    sex: "",
    age: null,
    status: "Pending", // Default value set to "Pending"
    resumeFile: "",
    imageFile: "",
    address: "",
    dateOfBirth: "",
    placeOfBirth: "",
    civilStatus: "",
    appliedJob: "",
  });

  const [errors, setErrors] = useState(null);
  const [loading, setLoading] = useState(false);
  const { setNotification } = useStateContext();
  const [imagePreview, setImagePreview] = useState("");

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    setImagePreview(URL.createObjectURL(file));
    setApplicant((prevState) => ({
      ...prevState,
      imageFile: file,
    }));
  };

  const handleChange = (e, field) => {
    const value = e.target.value;

    // Convert to uppercase only for specific fields
    const uppercasedValue = ["firstName", "middleName", "lastName"].includes(
      field
    )
      ? value.toUpperCase()
      : value;

    // Update the state with the uppercased value for specific fields
    setApplicant((prevState) => ({
      ...prevState,
      [field]: uppercasedValue,
    }));
  };

  useEffect(() => {
    if (id) {
      setLoading(true);
      axiosClient
        .get(`/applicants/${id}`)
        .then(({ data }) => {
          setLoading(false);
          const applicantData = data.data;
          setApplicant({
            id: applicantData.id,
            lastName: applicantData.last_name,
            firstName: applicantData.first_name,
            middleName: applicantData.middle_name,
            email: applicantData.email,
            phone: applicantData.phone,
            skills: applicantData.skills,
            highestEducationAttainment: applicantData.highest_educ_attainment,
            sex: applicantData.sex,
            age: applicantData.age,
            status: applicantData.status,
            resumeFile: applicantData.resume,
            imageFile: applicantData.image,
            address: applicantData.address,
            dateOfBirth: applicantData.date_of_birth,
            placeOfBirth: applicantData.place_of_birth,
            civilStatus: applicantData.civil_status,
            appliedJob: applicantData.applied_job,
          });
          if (applicantData.image) {
            setImagePreview(`/path/to/your/images/${applicantData.image}`);
          }
          console.log("Fetched data:", applicantData);
        })
        .catch((error) => {
          setLoading(false);
          console.error("Error fetching applicant data:", error);
        });
    }
  }, [id]);

  const onSubmit = (ev) => {
    ev.preventDefault();

    // Create payload with proper field names for the backend
    const payload = new FormData();
    payload.append("id", applicant.id);
    payload.append("last_name", applicant.lastName);
    payload.append("first_name", applicant.firstName);
    payload.append("middle_name", applicant.middleName);
    payload.append("email", applicant.email);
    payload.append("phone", applicant.phone);
    payload.append("skills", applicant.skills);
    payload.append(
      "highest_educ_attainment",
      applicant.highestEducationAttainment
    );
    payload.append("sex", applicant.sex);
    payload.append("age", applicant.age);
    payload.append("status", applicant.status);
    payload.append("resume", applicant.resumeFile);
    payload.append("image", applicant.imageFile);
    payload.append("address", applicant.address);
    payload.append("date_of_birth", applicant.dateOfBirth);
    payload.append("place_of_birth", applicant.placeOfBirth);
    payload.append("civil_status", applicant.civilStatus);
    payload.append("applied_job", applicant.appliedJob);

    if (applicant.id) {
      axiosClient
        .put(`/applicants/${applicant.id}`, payload)
        .then(() => {
          setNotification("Applicant was successfully updated");
          navigate("/applicants");
        })
        .catch((err) => {
          const response = err.response;
          if (response && response.status === 422) {
            setErrors(response.data.errors);
          }
        });
    } else {
      axiosClient
        .post("/applicants", payload)
        .then(() => {
          setNotification("Applicant was successfully created");
          navigate("/applicants");
        })
        .catch((err) => {
          const response = err.response;
          if (response && response.status === 422) {
            setErrors(response.data.errors);
          }
        });
    }
  };

  return (
    <>
      {applicant.id ? (
        <h1>
          Update Applicant: {applicant.firstName} {applicant.lastName}
        </h1>
      ) : (
        <h1>New Applicant</h1>
      )}
      {loading ? (
        <div className="text-center">Loading...</div>
      ) : (
        <>
          {errors && (
            <div className="alert">
              {Object.keys(errors).map((key) => (
                <p key={key}>{errors[key][0]}</p>
              ))}
            </div>
          )}
          <form
            onSubmit={onSubmit}
            style={{
              backgroundColor: "#EADFB4",
              padding: "20px",
              borderRadius: "8px",
            }}
          >
            <div className="container-fluid">
              <div className="row">
                <div className="col-lg-6" style={{ marginLeft: "10%" }}>
                  <div
                    className="card"
                    style={{
                      backgroundColor: "#9BB0C1",
                      padding: "20px",
                      borderRadius: "8px",
                    }}
                  >
                    <div className="card-body">
                      <div className="tab-content">
                        {/* Existing fields */}
                        <div className="nice-form-group">
                          <label htmlFor="last_name">Last Name</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="last_name"
                            value={applicant.lastName}
                            onChange={(e) => handleChange(e, "lastName")}
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="first_name">First Name</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="first_name"
                            value={applicant.firstName}
                            onChange={(e) => handleChange(e, "firstName")}
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="middle_name">Middle Name</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="middle_name"
                            value={applicant.middleName}
                            onChange={(e) => handleChange(e, "middleName")}
                          />
                        </div>

                        {/* New fields placed after middleName */}
                        <div className="nice-form-group">
                          <label htmlFor="address">Address</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="address"
                            value={applicant.address}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                address: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="date_of_birth">Date of Birth</label>
                          <input
                            type="date"
                            className="form-control nice-form"
                            id="date_of_birth"
                            value={applicant.dateOfBirth}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                dateOfBirth: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="place_of_birth">Place of Birth</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="place_of_birth"
                            value={applicant.placeOfBirth}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                placeOfBirth: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="civil_status">Civil Status</label>
                          <select
                            className="form-control nice-form"
                            id="civil_status"
                            value={applicant.civilStatus}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                civilStatus: e.target.value,
                              })
                            }
                          >
                            <option value="Single">Single</option>
                            <option value="Married">Married</option>
                            <option value="Widowed">Widowed</option>
                            <option value="Divorced">Divorced</option>
                          </select>
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="applied_job">Applied Job</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="applied_job"
                            value={applicant.appliedJob}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                appliedJob: e.target.value,
                              })
                            }
                          />
                        </div>

                        {/* Remaining fields */}
                        <div className="nice-form-group">
                          <label htmlFor="email">Email</label>
                          <input
                            type="email"
                            className="form-control nice-form"
                            id="email"
                            value={applicant.email}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                email: e.target.value,
                              })
                            }
                            required
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="phone">Phone</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="phone"
                            value={applicant.phone}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                phone: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="skills">Skills</label>
                          <input
                            type="text"
                            className="form-control nice-form"
                            id="skills"
                            value={applicant.skills}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                skills: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="highest_education_attainment">
                            Highest Education Attainment
                          </label>
                          <select
                            className="form-control nice-form"
                            id="highest_education_attainment"
                            value={applicant.highestEducationAttainment}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                highestEducationAttainment: e.target.value,
                              })
                            }
                          >
                            <option value="">Select an option</option>{" "}
                            {/* Empty option to indicate no selection */}
                            <option value="High School Graduate">
                              High School Graduate
                            </option>
                            <option value="Vocational">Vocational</option>
                            <option value="College Level">College Level</option>
                            <option value="College Graduate">
                              College Graduate
                            </option>
                            <option value="Post-Graduate">Post-Graduate</option>
                          </select>
                        </div>

                        <div className="nice-form-group">
                          <label htmlFor="sex">Sex</label>
                          <select
                            className="form-control nice-form"
                            id="sex"
                            value={applicant.sex}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                sex: e.target.value,
                              })
                            }
                          >
                            <option value="">Select an option</option>{" "}
                            {/* Empty option to indicate no selection */}
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                          </select>
                        </div>

                        <div className="nice-form-group">
                          <label htmlFor="age">Age</label>
                          <input
                            type="number"
                            className="form-control nice-form"
                            id="age"
                            value={applicant.age}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                age: parseInt(e.target.value) || null,
                              })
                            }
                          />
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="status">Status</label>
                          <select
                            className="form-control nice-form"
                            id="status"
                            value={applicant.status}
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                status: e.target.value,
                              })
                            }
                          >
                            <option value="Pending">Pending</option>
                            <option value="On-Review">On-Review</option>
                            <option value="Inactive">Inactive</option>
                          </select>
                        </div>

                        <div className="nice-form-group">
                          <label htmlFor="image">Profile Image</label>
                          <input
                            type="file"
                            className="form-control-file nice-form"
                            id="image"
                            onChange={handleImageChange}
                            accept=".jpg,.jpeg,.png"
                          />
                          {imagePreview && (
                            <div className="mt-3">
                              <img
                                src={imagePreview}

                                style={{
                                  width: "150px",
                                  height: "150px",
                                  objectFit: "cover",
                                }}
                              />
                            </div>
                          )}
                        </div>
                        <div className="nice-form-group">
                          <label htmlFor="resume">Resume</label>
                          <input
                            type="file"
                            className="form-control-file nice-form"
                            id="resume"
                            onChange={(e) =>
                              setApplicant({
                                ...applicant,
                                resumeFile: e.target.files[0],
                              })
                            }
                            accept=".pdf,.doc,.docx"
                          />
                        </div>
                        <div className="nice-form-group">
                          <button
                            className="button button2"
                            type="submit"
                            name="submit"
                          >
                            Submit
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </>
      )}
    </>
  );
}
