.irs--modern {
    @name: irs;

    @top: 25px;
    @bottom: 21px;
    @line_height: 5px;
    @handle_width: 12px;
    @handle_height: 13px;

    @line_color: #d1d6e0;
    @bar_color: #20b426;
    @handle_color_1: #e9e6e6;
    @handle_color_2: white;
    @handle_color_3: black;
    @minmax_text_color: white;
    @minmax_bg_color: #d1d6e0;
    @label_color_1: #20b426;
    @label_color_2: white;
    @grid_color_1: #dedede;
    @grid_color_2: silver;



    height: 55px;

    &.irs-with-grid {
        height: 55px;
    }

    .@{name}-line {
        top: @top;
        height: @line_height;
        background-color: @line_color;
        background: linear-gradient(to bottom, lighten(@line_color, 5%) 0%, @line_color 100%);
        border: 1px solid darken(@line_color, 15%);
        border-bottom-width: 0;
        border-radius: @line_height;
    }

    .@{name}-bar {
        top: @top;
        height: @line_height;
        background: @bar_color;
        background: linear-gradient(to bottom, @bar_color 0%, darken(@bar_color, 10%) 100%);

        &--single {
            border-radius: @line_height 0 0 @line_height;
        }
    }

    .@{name}-shadow {
        height: 1px;
        bottom: @bottom;
        background-color: fade(@line_color, 50%);
    }

    .@{name}-handle {
        top: 37px;
        width: @handle_width;
        height: @handle_height;
        border: 1px solid darken(@line_color, 15%);
        border-top-width: 0;
        box-shadow: 1px 1px 1px rgba(0,0,0,0.1);
        border-radius: 0 0 3px 3px;

        & > i:nth-child(1) {
            position: absolute;
            display: block;
            top: -4px; left: 1px;
            width: (@handle_width - 6px);
            height: (@handle_width - 6px);
            border: 1px solid darken(@line_color, 15%);
            background: @handle_color_2;
            transform: rotate(45deg);
        }

        & > i:nth-child(2) {
            position: absolute;
            display: block;
            box-sizing: border-box;
            top: 0; left: 0;
            width: (@handle_width - 2);
            height: (@handle_height - 1);
            background: @handle_color_1;
            background: linear-gradient(to bottom, @handle_color_2 0%, @handle_color_1 100%);
            border-radius: 0 0 3px 3px;
        }

        & > i:nth-child(3) {
            position: absolute;
            display: block;
            box-sizing: border-box;
            top: 3px; left: 3px;
            width: 4px; height: 5px;
            border-left: 1px solid darken(@line_color, 15%);
            border-right: 1px solid darken(@line_color, 15%);
        }

        &.state_hover,
        &:hover {
            border-color: darken(@line_color, 30%);
            background: #c3c7cd;
            background: linear-gradient(to bottom, rgba(255,255,255,1) 0%,rgba(145,155,165,1) 30%,rgba(255,255,255,1) 100%);

            & > i:nth-child(1) {
                border-color: darken(@line_color, 30%);
            }

            & > i:nth-child(3) {
                border-color: darken(@line_color, 50%);
            }
        }
    }

    .@{name}-min,
    .@{name}-max {
        top: 0;
        font-size: 10px;
        line-height: 1.333;
        text-shadow: none;
        padding: 1px 5px;
        color: @minmax_text_color;
        background-color: @minmax_bg_color;
        border-radius: @line_height;
    }

    .@{name}-from,
    .@{name}-to,
    .@{name}-single {
        font-size: 10px;
        line-height: 1.333;
        text-shadow: none;
        padding: 1px 5px;
        background-color: @label_color_1;
        color: @label_color_2;
        border-radius: @line_height;

        &:before {
            position: absolute;
            display: block;
            content: "";
            bottom: -6px;
            left: 50%;
            width: 0;
            height: 0;
            margin-left: -3px;
            overflow: hidden;
            border: 3px solid transparent;
            border-top-color: @label_color_1;
        }
    }

    .@{name}-grid {
        height: 25px;

        &-pol {
            background-color: @grid_color_1;
        }

        &-text {
            color: @grid_color_2;
            font-size: 13px;
        }
    }
}
