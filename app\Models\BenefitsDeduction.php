<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BenefitsDeductions extends Model
{
    protected $fillable = [
        'contract_id',
        'ec_ee_share',
        'ec_er_share',
        'ec_gov',
        'hdmf_ee_share',
        'hdmf_er_share',
        'hdmf_gov',
        'phic_ee_share',
        'phic_er_share',
        'phic_gov',
        'ss_ee_share',
        'sss_er_share',
        'sss_gov',
        'datecreated',
    ];

    // Define relationships if needed
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
}

