<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PayrollResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'contract_id' => $this->contract_id,
            'cut_off_date' => $this->cut_off_date,
            'rw_hrs' => $this->rw_hrs,
            'rw_day' => $this->rw_day,
            'daily_rate' => $this->daily_rate,
            'basic_salary' => $this->basic_salary,
            'thirteenth_month_pay' => $this->thirteenth_month_pay,
            'sil' => $this->sil,
            'gross_pay' => $this->gross_pay,
            'total_deductions' => $this->total_deductions,
            'net_pay' => $this->net_pay,
            'datecreated' => $this->datecreated,
            'dateupdated' => $this->dateupdated,
            'applicant_name' => $this->contracts->applicant->last_name . ', ' . $this->contracts->applicant->first_name . ' ' . $this->contracts->applicant->middle_name,
            'company_name' => $this->contracts->jobTitle->client->company_name,
            'designation' => $this->contracts->designation,
        ];
    }
}
