<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreApplicantRequest extends FormRequest
{
    public function authorize()
    {
        return true; // You can define authorization logic here if needed
    }

    public function rules()
    {
        return [
            'last_name' => 'required|string|max:255',
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'email' => 'required|email|unique:applicants,email',
            'phone' => 'required|string|max:20',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:2048', // Accepting files with specified types and max size of 2MB
            'skills' => 'nullable|string|max:255',
            'highest_educ_attainment' => 'nullable|string|max:255',
            'sex' => 'nullable|string|max:255',
            'age' => 'nullable|integer',
            'status' => 'required|string|max:255',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048', // Accepting image files with specified types and max size of 2MB
            'address' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date', // Validates date format
            'place_of_birth' => 'nullable|string|max:255',
            'civil_status' => 'nullable|string|max:255',
            'applied_job' => 'nullable|string|max:255',
        ];
    }
}
