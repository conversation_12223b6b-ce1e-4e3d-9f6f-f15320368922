import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import axiosClient from "../../axios-client.js";
import { useStateContext } from "../../contexts/ContextProvider.jsx";

export default function PayrollForm() {
  const [selectedComponent, setSelectedComponent] = useState("");
  const [hours, setHours] = useState("");
  const [components, setComponents] = useState([]);
  const [showModal, setShowModal] = useState(false);

  const handleSelectComponent = (e) => {
    setSelectedComponent(e.target.value);
  };

  const handleModalClose = () => setShowModal(false);
  const handleModalShow = () => setShowModal(true);

  const handleModalSave = () => {
    const component = {
      type: selectedComponent,
      hours: parseFloat(hours),
      value: calculateComponentValue(selectedComponent, parseFloat(hours)),
    };
    setComponents([...components, component]);
    setSelectedComponent("");
    setHours("");
    handleModalClose();
  };
  ///////////////
  const navigate = useNavigate();
  const { id } = useParams();

  const [applicants, setApplicants] = useState([]);
  const [jobTitles, setJobTitles] = useState([]);
  const [contract, setContract] = useState({
    id: null,
    applicantId: "",
    jobtitleId: "",
    designation: "",
    dailyRate: "",
  });
  const [applicantName, setApplicantName] = useState("");
  const [jobTitleName, setJobTitleName] = useState("");
  const [errors, setErrors] = useState(null);
  const [loading, setLoading] = useState(false);
  const { setNotification } = useStateContext();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [applicantsResponse, jobTitlesResponse] = await Promise.all([
          axiosClient.get("/applicants"),
          axiosClient.get("/jobtitles"),
        ]);

        setApplicants(applicantsResponse.data.data);
        setJobTitles(jobTitlesResponse.data.data);
      } catch (error) {
        console.error("Error fetching applicants or job titles:", error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchContractData = async () => {
      if (id) {
        setLoading(true);
        try {
          const contractResponse = await axiosClient.get(`/contracts/${id}`);
          const contractData = contractResponse.data.data;
          setContract({
            id: contractData.id,
            applicantId: contractData.applicant_id,
            jobtitleId: contractData.jobtitle_id,
            startDate: contractData.start_date,
            endDate: contractData.end_date,
            designation: contractData.designation,
            dailyRate: contractData.daily_rate,
            empStatus: contractData.emp_status,
            remarks: contractData.remarks,
          });

          // Fetch corresponding applicant data
          const applicantResponse = await axiosClient.get(
            `/applicants/${contractData.applicant_id}`
          );
          const applicantData = applicantResponse.data.data;
          setApplicantName(
            `${applicantData.first_name} ${applicantData.last_name}`
          );

          // Fetch corresponding job title data
          const jobTitleResponse = await axiosClient.get(
            `/jobtitles/${contractData.jobtitle_id}`
          );
          const jobTitleData = jobTitleResponse.data.data;
          setJobTitleName(jobTitleData.title);
        } catch (error) {
          console.error(
            "Error fetching contract, applicant, or job title data:",
            error
          );
        } finally {
          setLoading(false);
        }
      }
    };

    fetchContractData();
  }, [id]);

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
  };

  const calculateComponentValue = (type, hours) => {
    // Calculate based on type and hours
    // Example implementation
    const hourlyRate = 100; // Example hourly rate
    const dailyRate = hourlyRate * 8; // Example daily rate calculation

    switch (type) {
      case "Basic Overtime":
        return hourlyRate * 1.25 * hours;
      case "Night Shift Differential":
        return hourlyRate * 0.1 * hours;
      case "Night Shift Differential Overtime":
        return hourlyRate * 1.25 * 0.1 * hours;
      case "Regular Day Differential":
        return dailyRate;
      case "Regular Day Differential Overtime":
        return hourlyRate * 1.25 * hours;
      case "Regular Day Night Shift Differential":
        return hourlyRate * 1.1 * hours;
      case "Regular Day Night Shift Differential Overtime":
        return hourlyRate * 1.25 * 1.1 * hours;
      case "Special Holiday":
        return dailyRate * 1.3;
      case "Special Holiday Overtime":
        return hourlyRate * 1.3 * 1.25 * hours;
      case "Special Holiday Night Shift Differential":
        return hourlyRate * 1.3 * 0.1 * hours;
      case "Special Holiday Night Shift Differential Overtime":
        return hourlyRate * 1.3 * 1.25 * 0.1 * hours;
      case "Legal Holiday":
        return dailyRate * 2;
      case "Legal Holiday Overtime":
        return hourlyRate * 2 * 1.3 * hours;
      case "Legal Holiday Night Shift Differential":
        return hourlyRate * 2 * 0.1 * hours;
      case "Legal Holiday Night Shift Differential Overtime":
        return hourlyRate * 2 * 1.3 * 0.1 * hours;
      case "Legal Holiday Regular Day":
        return dailyRate * 2;
      case "Legal Holiday Regular Day Overtime":
        return hourlyRate * 2 * 1.3 * hours;
      case "Legal Holiday Regular Day Night Shift Differential":
        return hourlyRate * 2 * 0.1 * hours;
      case "Legal Holiday Regular Day Night Shift Differential Overtime":
        return hourlyRate * 2 * 1.3 * 0.1 * hours;
      case "Special Holiday Regular Day":
        return dailyRate * 1.3;
      case "Special Holiday Regular Day Overtime":
        return hourlyRate * 1.3 * 1.25 * hours;
      case "Special Holiday Regular Day Night Shift Differential":
        return hourlyRate * 1.3 * 0.1 * hours;
      case "Special Holiday Regular Day Night Shift Differential Overtime":
        return hourlyRate * 1.3 * 1.25 * 0.1 * hours;
      default:
        return 0;
    }
  };

  return (
    <div className="container">
      <h2>Create Payroll</h2>

      <form>
        <input type="hidden" name="contract_id" />

        <div className="form-group">
          <h4>Employee Info</h4>
          <div className="row">
            <div className="col-md-6">
              <label htmlFor="fullname">Name:</label>
              <input
                type="text"
                id="fullname"
                name="fullname"
                className="form-control"
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="company_name">Company:</label>
              <input
                type="text"
                id="company_name"
                name="company_name"
                className="form-control"
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="designation">Designation:</label>
              <input
                type="text"
                id="designation"
                name="designation"
                className="form-control"
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="period_start">Cut-off Date:</label>
              <input
                type="date"
                id="period_start"
                name="period_start"
                className="form-control"
              />
            </div>
          </div>
        </div>

        <div className="form-group">
          <h4>Total Days</h4>
          <div className="row">
            <div className="col-md-6">
              <label htmlFor="total_days">Total Work Days:</label>
              <input
                type="number"
                id="total_days"
                name="total_days"
                className="form-control"
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="total_hours">Total Work Hours:</label>
              <input
                type="number"
                id="total_hours"
                name="total_hours"
                className="form-control"
                readOnly
              />
            </div>
          </div>
        </div>

        <div className="form-group">
          <h4>Basic Salary</h4>
          <div className="row">
            <div className="col-md-6">
              <label htmlFor="daily_rate">Daily Rate:</label>
              <input
                type="number"
                step="0.01"
                id="daily_rate"
                name="daily_rate"
                className="form-control exclude-default"
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="basic_salary">Basic Salary:</label>
              <input
                type="number"
                step="0.01"
                id="basic_salary"
                name="basic_salary"
                className="form-control"
                readOnly
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="b_ot">Basic Overtime (hours):</label>
              <input
                type="number"
                step="0.01"
                id="b_ot"
                name="b_ot"
                className="form-control"
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="b_ot_amt">Amount:</label>
              <input
                type="number"
                step="0.01"
                className="form-control"
                id="b_ot_amt"
                name="b_ot_amt"
                readOnly
              />
            </div>
          </div>
        </div>

        <div className="form-group">
          <h4>Payroll Components</h4>
          <div className="form-group">
            <select
              className="form-select"
              onChange={handleSelectComponent}
              value={selectedComponent}
            >
              <option value="">Choose...</option>
              <option value="Basic Overtime">Basic Overtime</option>
              <option value="Night Shift Differential">
                Night Shift Differential
              </option>
              <option value="Night Shift Differential Overtime">
                Night Shift Differential Overtime
              </option>
              <option value="Regular Day Differential">
                Regular Day Differential
              </option>
              <option value="Regular Day Differential Overtime">
                Regular Day Differential Overtime
              </option>
              <option value="Regular Day Night Shift Differential">
                Regular Day Night Shift Differential
              </option>
              <option value="Regular Day Night Shift Differential Overtime">
                Regular Day Night Shift Differential Overtime
              </option>
              <option value="Special Holiday">Special Holiday</option>
              <option value="Special Holiday Overtime">
                Special Holiday Overtime
              </option>
              <option value="Special Holiday Night Shift Differential">
                Special Holiday Night Shift Differential
              </option>
              <option value="Special Holiday Night Shift Differential Overtime">
                Special Holiday Night Shift Differential Overtime
              </option>
              <option value="Legal Holiday">Legal Holiday</option>
              <option value="Legal Holiday Overtime">
                Legal Holiday Overtime
              </option>
              <option value="Legal Holiday Night Shift Differential">
                Legal Holiday Night Shift Differential
              </option>
              <option value="Legal Holiday Night Shift Differential Overtime">
                Legal Holiday Night Shift Differential Overtime
              </option>
              <option value="Legal Holiday Regular Day">
                Legal Holiday Regular Day
              </option>
              <option value="Legal Holiday Regular Day Overtime">
                Legal Holiday Regular Day Overtime
              </option>
              <option value="Legal Holiday Regular Day Night Shift Differential">
                Legal Holiday Regular Day Night Shift Differential
              </option>
              <option value="Legal Holiday Regular Day Night Shift Differential Overtime">
                Legal Holiday Regular Day Night Shift Differential Overtime
              </option>
              <option value="Special Holiday Regular Day">
                Special Holiday Regular Day
              </option>
              <option value="Special Holiday Regular Day Overtime">
                Special Holiday Regular Day Overtime
              </option>
              <option value="Special Holiday Regular Day Night Shift Differential">
                Special Holiday Regular Day Night Shift Differential
              </option>
              <option value="Special Holiday Regular Day Night Shift Differential Overtime">
                Special Holiday Regular Day Night Shift Differential Overtime
              </option>
            </select>
            <button
              type="button"
              className="btn btn-primary mt-2"
              onClick={handleModalShow}
            >
              Add Component
            </button>
          </div>
        </div>
        <div className="form-group">
          <h4>Components List</h4>
          <ul className="list-group">
            {components.map((comp, index) => (
              <li key={index} className="list-group-item">
                {comp.type}: {comp.hours} hours - PHP {comp.value.toFixed(2)}
              </li>
            ))}
          </ul>
        </div>

        <button type="submit" className="btn btn-primary mt-3">
          Submit
        </button>
      </form>

      {/* Modal */}
      <div
        className={`modal fade ${showModal ? "show" : ""}`}
        style={{ display: showModal ? "block" : "none" }}
        tabIndex="-1"
        role="dialog"
      >
        <div className="modal-dialog" role="document">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">
                Enter Hours for {selectedComponent}
              </h5>
              <button
                type="button"
                className="close"
                onClick={handleModalClose}
              >
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="hours">Hours:</label>
                <input
                  type="number"
                  id="hours"
                  className="form-control"
                  value={hours}
                  onChange={(e) => setHours(e.target.value)}
                />
              </div>
            </div>
            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleModalClose}
              >
                Close
              </button>
              <button
                type="button"
                className="btn btn-primary"
                onClick={handleModalSave}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
