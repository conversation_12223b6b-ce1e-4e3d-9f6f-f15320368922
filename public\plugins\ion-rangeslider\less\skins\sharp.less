.irs--sharp {
    @name: irs;

    @top: 30px;
    @bottom: 21px;
    @line_height: 2px;
    @handle_width: 10px;
    @handle_height: 10px;

    @line_color: black;
    @bar_color: #ee22fa;
    @handle_color_1: darken(#ee22fa, 20%);
    @handle_color_2: white;
    @handle_color_3: black;
    @minmax_text_color: white;
    @minmax_bg_color: darken(#ee22fa, 20%);
    @label_color_1: darken(#ee22fa, 20%);
    @label_color_2: white;
    @grid_color_1: #dedede;
    @grid_color_2: silver;



    height: 50px;
    font-size: 12px;
    line-height: 1;

    &.irs-with-grid {
        height: 57px;
    }

    .@{name}-line {
        top: @top;
        height: @line_height;
        background-color: @line_color;
        border-radius: @line_height;
    }

    .@{name}-bar {
        top: @top;
        height: @line_height;
        background-color: @bar_color;

        &--single {
            border-radius: @line_height 0 0 @line_height;
        }
    }

    .@{name}-shadow {
        height: 1px;
        bottom: @bottom;
        background-color: fade(@line_color, 50%);
    }

    .@{name}-handle {
        top: (@top - 5px);
        width: @handle_width;
        height: @handle_height;
        background-color: @handle_color_1;

        & > i:first-child {
            position: absolute;
            display: block;
            top: 100%;
            left: 0;
            width: 0;
            height: 0;
            border: 5px solid transparent;
            border-top-color: @handle_color_1;
        }

        &.state_hover,
        &:hover {
            background-color: @handle_color_3;

            & > i:first-child {
                border-top-color: @handle_color_3;
            }
        }
    }

    .@{name}-min,
    .@{name}-max {
        color: @minmax_text_color;
        font-size: 14px;
        line-height: 1;
        top: 0;
        padding: 3px 4px;
        opacity: 0.4;
        background-color: @minmax_bg_color;
        border-radius: @line_height;
    }

    .@{name}-from,
    .@{name}-to,
    .@{name}-single {
        font-size: 14px;
        line-height: 1;
        text-shadow: none;
        padding: 3px 4px;
        background-color: @label_color_1;
        color: @label_color_2;
        border-radius: @line_height;

        &:before {
            position: absolute;
            display: block;
            content: "";
            bottom: -6px;
            left: 50%;
            width: 0;
            height: 0;
            margin-left: -3px;
            overflow: hidden;
            border: 3px solid transparent;
            border-top-color: @label_color_1;
        }
    }

    .@{name}-grid {
        height: 25px;

        &-pol {
            background-color: @grid_color_1;
        }

        &-text {
            color: @grid_color_2;
            font-size: 13px;
        }
    }
}
