<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AmtOtNdHoliday;
use App\Http\Requests\StoreAmtOtNdHolidayRequest;
use App\Http\Requests\UpdateAmtOtNdHolidayRequest;
use App\Http\Resources\AmtOtNdHolidayResource;
use Illuminate\Http\Request;

class AmtOtNdHolidayController extends Controller
{
    public function index()
    {
        return AmtOtNdHolidayResource::collection(AmtOtNdHoliday::all());
    }

    public function store(StoreAmtOtNdHolidayRequest $request)
    {
        $amtOtNdHoliday = AmtOtNdHoliday::create($request->validated());
        return new AmtOtNdHolidayResource($amtOtNdHoliday);
    }

    public function show(AmtOtNdHoliday $amtOtNdHoliday)
    {
        return new AmtOtNdHolidayResource($amtOtNdHoliday);
    }

    public function update(UpdateAmtOtNdHolidayRequest $request, AmtOtNdHoliday $amtOtNdHoliday)
    {
        $amtOtNdHoliday->update($request->validated());
        return new AmtOtNdHolidayResource($amtOtNdHoliday);
    }

    public function destroy(AmtOtNdHoliday $amtOtNdHoliday)
    {
        $amtOtNdHoliday->delete();
        return response()->noContent();
    }
}
