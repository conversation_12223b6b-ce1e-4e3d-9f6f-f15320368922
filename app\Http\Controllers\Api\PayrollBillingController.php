<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PayrollBilling;
use App\Http\Requests\StorePayrollBillingRequest;
use App\Http\Requests\UpdatePayrollBillingRequest;
use App\Http\Resources\PayrollBillingResource;
use Illuminate\Http\Request;

class PayrollBillingController extends Controller
{
    public function index()
    {
        return PayrollBillingResource::collection(PayrollBilling::all());
    }

    public function store(StorePayrollBillingRequest $request)
    {
        $payrollBilling = PayrollBilling::create($request->validated());
        return new PayrollBillingResource($payrollBilling);
    }

    public function show(PayrollBilling $payrollBilling)
    {
        return new PayrollBillingResource($payrollBilling);
    }

    public function update(UpdatePayrollBillingRequest $request, PayrollBilling $payrollBilling)
    {
        $payrollBilling->update($request->validated());
        return new PayrollBillingResource($payrollBilling);
    }

    public function destroy(PayrollBilling $payrollBilling)
    {
        $payrollBilling->delete();
        return response()->noContent();
    }
}
