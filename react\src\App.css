#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* DefaultLayout */

.sidebar-footer {
  padding: 1rem;
  background-color: #f8f9fa; /* Light background for the footer */
  text-align: center;
  border-top: 1px solid #dee2e6; /* Optional border for separation */
  width: 100%; /* Ensures the footer spans the full width of the sidebar */
  position: relative; /* Ensure the footer stays at the bottom */
  bottom: 0; /* Align to the bottom of the container */
}

.sidebar {
  position: sticky;
  top: 0;
  height: 100vh; /* Full viewport height */
  display: flex;
  flex-direction: column;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar-header {
  flex-shrink: 0; /* Prevents the header from shrinking */
}

.accordion-scrollable {
  overflow-y: auto;
  flex-grow: 1; /* Takes up remaining space */
  padding-bottom: 1rem; /* Ensure padding at the bottom */
}

/* Optional: Adjusting padding and margins for better spacing */
.accordion-body {
  padding: 0;
}

.accordion-item {
  margin-bottom: 0.5rem;
}

/* Style for notification container */
.notification-container {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1050; /* Adjust based on your design */
  transition: opacity 0.5s ease;
}

.notification-container .alert {
  margin: 0;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.781); /* Semi-transparent background */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background: rgb(255, 255, 255); /* White background for the modal content */
  padding: 20px;
  border-radius: 5px;
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.modal-buttons {
  margin-top: 20px;
}

.modal-buttons .btn {
  margin: 0 10px;
}
