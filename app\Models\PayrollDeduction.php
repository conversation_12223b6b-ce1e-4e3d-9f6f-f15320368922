<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PayrollDeduction extends Model
{
    use HasFactory;

    protected $fillable = [
        'payroll_id', 'hrs_late_ut', 'amt_late_ut', 'sss_loan', 'hdmf_loan',
        'uniform_ppe', 'insurance', 'other_loans', 'other_deduc'
    ];

    public function payroll()
    {
        return $this->belongsTo(Payroll::class);
    }
}
