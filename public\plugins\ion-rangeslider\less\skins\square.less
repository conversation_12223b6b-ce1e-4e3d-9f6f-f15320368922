// Skin design by <PERSON><PERSON><PERSON><PERSON>
// https://github.com/grimals<PERSON>

.irs--square {
    @name: irs;

    @top: 31px;
    @bottom: 21px;
    @line_height: 4px;
    @handle_width: 16px;
    @handle_height: 16px;

    @line_color: #dedede;
    @bar_color: black;
    @handle_color_1: black;
    @handle_color_2: white;
    @handle_color_3: #f0f6ff;
    @minmax_text_color: #333;
    @minmax_bg_color: rgba(0,0,0,0.1);
    @label_color_1: black;
    @label_color_2: white;
    @grid_color_1: #dedede;
    @grid_color_2: silver;



    height: 50px;

    &.irs-with-grid {
        height: 60px;
    }

    .@{name}-line {
        top: @top;
        height: @line_height;
        background-color: @line_color;
    }

    .@{name}-bar {
        top: @top;
        height: @line_height;
        background-color: @bar_color;
    }

    .@{name}-shadow {
        height: 2px;
        bottom: @bottom;
        background-color: @line_color;
    }

    .@{name}-handle {
        top: (@top - 6px);
        width: @handle_width;
        height: @handle_height;
        border: 3px solid @handle_color_1;
        background-color: @handle_color_2;
        -webkit-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        transform: rotate(45deg);

        &.state_hover,
        &:hover {
            background-color: @handle_color_3;
        }
    }

    .@{name}-min,
    .@{name}-max {
        color: @minmax_text_color;
        font-size: 14px;
        line-height: 1;
        top: 0;
        padding: 3px 5px;
        background-color: @minmax_bg_color;
    }

    .@{name}-from,
    .@{name}-to,
    .@{name}-single {
        font-size: 14px;
        line-height: 1;
        text-shadow: none;
        padding: 3px 5px;
        background-color: @label_color_1;
        color: @label_color_2;
    }

    .@{name}-grid {
        height: 25px;

        &-pol {
            background-color: @grid_color_1;
        }

        &-text {
            color: @grid_color_2;
            font-size: 11px;
        }
    }
}
