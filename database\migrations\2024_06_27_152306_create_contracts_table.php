<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractsTable extends Migration
{
    public function up()
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('applicant_id');
            $table->unsignedBigInteger('jobtitle_id');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->string('designation')->nullable();
            $table->decimal('daily_rate', 6, 2)->nullable();
            $table->string('emp_status', 50);
            $table->text('remarks')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('applicant_id')->references('id')->on('applicants');
            $table->foreign('jobtitle_id')->references('id')->on('job_titles');
        });
    }

    public function down()
    {
        Schema::dropIfExists('contracts');
    }
}
