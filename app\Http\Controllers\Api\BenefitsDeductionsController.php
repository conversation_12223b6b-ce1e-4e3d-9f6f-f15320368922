<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BenefitsDeductions;
use Illuminate\Http\Request;

class BenefitsDeductionsController extends Controller
{
    public function index()
    {
        return BenefitsDeductions::all();
    }

    public function store(Request $request)
    {
        return BenefitsDeductions::create($request->all());
    }

    public function show(BenefitsDeductions $benefitsDeductions)
    {
        return $benefitsDeductions;
    }

    public function update(Request $request, BenefitsDeductions $benefitsDeductions)
    {
        $benefitsDeductions->update($request->all());
        return $benefitsDeductions;
    }

    public function destroy(BenefitsDeductions $benefitsDeductions)
    {
        $benefitsDeductions->delete();
        return response()->json(null, 204);
    }
}
