<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('payroll_billings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payroll_id')->constrained('payroll')->onDelete('cascade')->onUpdate('cascade');
            $table->decimal('insurance', 10, 4);
            $table->decimal('uniform_ppe', 10, 4);
            $table->decimal('late_ut', 10, 4);
            $table->decimal('gross', 10, 4);
            $table->decimal('net_pay', 10, 4);
            $table->decimal('basic_pay', 10, 4);
            $table->decimal('payable_ee', 10, 4);
            $table->decimal('payable_gov', 10, 4);
            $table->decimal('sub_billing', 10, 4);
            $table->decimal('admin_fee', 10, 4);
            $table->decimal('admin_subtotal', 10, 4);
            $table->decimal('gross_pay_basic', 10, 4);
            $table->decimal('add_salaries', 10, 4);
            $table->decimal('basic_billing', 10, 4);
            $table->decimal('vat', 10, 4);
            $table->decimal('total_billing', 10, 4);
            $table->timestamp('datecreated')->useCurrent();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payroll_billings');
    }
};
