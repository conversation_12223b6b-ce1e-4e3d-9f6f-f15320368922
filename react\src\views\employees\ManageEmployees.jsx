import { useEffect, useState } from "react";
import axiosClient from "../../axios-client";
import { Link } from "react-router-dom";
import { useStateContext } from "../../contexts/ContextProvider";
import "bootstrap/dist/css/bootstrap.min.css";

export default function ManageEmployees() {
  const [contracts, setContracts] = useState([]);
  const [applicantData, setApplicantData] = useState({}); // Update state to store image
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { setNotification } = useStateContext();

  useEffect(() => {
    getContracts();
  }, [currentPage]);

  const onDeleteClick = (contract) => {
    showModal({
      message: "Are you sure you want to delete this contract?",
      onConfirm: () => {
        axiosClient
          .delete(`/contracts/${contract.id}`)
          .then(() => {
            setNotification("Contract was successfully deleted");
            getContracts();
          })
          .catch((error) => {
            console.error("Error deleting contract:", error);
          });
      },
    });
  };

  const getContracts = () => {
    setLoading(true);
    axiosClient
      .get(`/contracts?page=${currentPage}`)
      .then(({ data }) => {
        setLoading(false);
        setContracts(data.data);
        setTotalPages(data.meta.last_page);

        // Fetch applicant data for each contract
        data.data.forEach((contract) => {
          getApplicantData(contract.applicant_id);
        });
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const getApplicantData = (applicantId) => {
    if (
      applicantData[applicantId] &&
      applicantData[applicantId].loading === false
    ) {
      return; // Already fetched
    }

    setApplicantData((prevData) => ({
      ...prevData,
      [applicantId]: { loading: true }, // Set loading to true while fetching
    }));

    axiosClient
      .get(`/applicants/${applicantId}`)
      .then(({ data }) => {
        const applicant = data.data;
        setApplicantData((prevData) => ({
          ...prevData,
          [applicantId]: {
            name: `${applicant.first_name} ${applicant.middle_name || ""} ${
              applicant.last_name
            }`,
            image: applicant.image,
            loading: false, // Set loading to false when done
            firstName: applicant.first_name,
            middleName: applicant.middle_name || "",
            lastName: applicant.last_name,
          },
        }));
      })
      .catch((error) => {
        console.error("Error fetching applicant data:", error);
        setApplicantData((prevData) => ({
          ...prevData,
          [applicantId]: { loading: false }, // Set loading to false on error
        }));
      });
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const goToFirstPage = () => {
    setCurrentPage(1);
  };

  const goToLastPage = () => {
    setCurrentPage(totalPages);
  };

  const getPageNumbers = () => {
    const currentPageIndex = currentPage - 1;
    const maxPagesToShow = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = startPage + maxPagesToShow - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    return Array.from(
      { length: endPage - startPage + 1 },
      (_, index) => startPage + index
    );
  };

  return (
    <main className="container mt-5">
      <div className="row mb-3">
        <div className="col-md-6">
          <h1>Manage Employees</h1>
        </div>
        <div className="col-md-6 text-end">
          <Link className="btn btn-primary" to="/contracts/new">
            Add New
          </Link>
        </div>
      </div>
      <div className="row">
        {loading ? (
          <div className="col-12 text-center">Loading...</div>
        ) : contracts.length === 0 ? (
          <div className="col-12 text-center">No contracts found</div>
        ) : (
          contracts.map((contract) => (
            <div className="col-md-4 mb-4" key={contract.id}>
              <div className="card h-100 d-flex flex-column position-relative">
                <img
                  src={
                    applicantData[contract.applicant_id]?.image
                      ? `/public/${applicantData[contract.applicant_id].image}` // Corrected image URL path
                      : "/default.png" // Fallback image
                  }
                  style={{
                    width: "35%",
                    height: "120px",
                    objectFit: "fill",
                    position: "absolute",
                    top: "10px",
                    right: "10px",
                    borderRadius: "8px",
                  }}
                  alt={`Image of ${
                    applicantData[contract.applicant_id]?.lastName
                  }`}
                />

                <div className="card-body d-flex flex-column">
                  <h5 className="card-title">
                    {applicantData[contract.applicant_id]?.loading ? (
                      <div>Loading...</div>
                    ) : (
                      <>
                        <div
                          style={{
                            fontSize: "1.5rem",
                            fontWeight: "bold",
                          }}
                        >
                          {applicantData[contract.applicant_id]?.lastName}
                        </div>
                        <div
                          style={{
                            fontSize: "1rem",
                            textAlign: "left",
                          }}
                        >
                          {applicantData[contract.applicant_id]?.firstName}{" "}
                          {applicantData[
                            contract.applicant_id
                          ]?.middleName.charAt(0)}
                          .
                        </div>
                      </>
                    )}
                  </h5>
                  <p className="card-text">{contract.designation}</p>
                  <p className="card-text">{contract.daily_rate}</p>

                  <p className="card-text border p-2 rounded">
                    {contract.remarks}
                  </p>
                  <div className="mt-auto d-flex justify-content-center">
                    <div
                      className="btn-group"
                      role="group"
                      aria-label="Actions"
                    >
                      <Link
                        className="btn btn-primary mx-1"
                        to={`/contracts/${contract.id}`}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="Edit"
                      >
                        <i className="bi bi-pencil"></i>
                      </Link>
                      <Link
                        className="btn btn-info mx-1"
                        to={`/payroll/${contract.id}`}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="Payroll"
                      >
                        <i className="bi bi-file-earmark-text"></i>
                      </Link>
                      <button
                        className="btn btn-danger mx-1"
                        onClick={() => onDeleteClick(contract)}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="Delete"
                      >
                        <i className="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {totalPages > 1 && (
        <nav aria-label="Page navigation example">
          <ul className="pagination justify-content-center mt-3">
            <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
              <button
                className="page-link"
                onClick={goToFirstPage}
                disabled={currentPage === 1}
              >
                &laquo;
              </button>
            </li>
            {getPageNumbers().map((page) => (
              <li
                key={page}
                className={`page-item ${currentPage === page ? "active" : ""}`}
              >
                <button
                  className="page-link"
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </button>
              </li>
            ))}
            <li
              className={`page-item ${
                currentPage === totalPages ? "disabled" : ""
              }`}
            >
              <button
                className="page-link"
                onClick={goToLastPage}
                disabled={currentPage === totalPages}
              >
                &raquo;
              </button>
            </li>
          </ul>
        </nav>
      )}
    </main>
  );
}
