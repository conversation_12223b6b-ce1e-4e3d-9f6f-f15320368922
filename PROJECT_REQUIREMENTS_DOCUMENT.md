# Project Requirements Document (PRD)
## EPMSI Human Resource Management System

### Document Information
- **Project Name**: Eastern Pride Manpower Services, Inc. (EPMSI) HR Management System
- **Version**: 2.0 (<PERSON><PERSON> 12 Upgrade)
- **Date**: December 2024
- **Document Type**: Project Requirements Document
- **Technology Stack**: <PERSON><PERSON> 12 + React 18 + MySQL

---

## 1. Executive Summary

### 1.1 Project Overview
The EPMSI HR Management System is a comprehensive web-based application designed to streamline human resource operations for Eastern Pride Manpower Services, Inc. The system manages the complete employee lifecycle from recruitment and applicant tracking to payroll processing and client management.

### 1.2 Business Objectives
- Automate HR processes and reduce manual paperwork
- Centralize employee data and client information
- Streamline recruitment and hiring workflows
- Automate payroll calculations and government compliance
- Provide real-time reporting and analytics
- Ensure data security and access control

### 1.3 Target Users
- **HR Administrators**: Full system access for managing all HR operations
- **Recruiters**: Access to applicant and job posting management
- **Payroll Officers**: Access to payroll processing and calculations
- **Management**: Access to reports and analytics

---

## 2. System Architecture

### 2.1 Technology Stack
- **Backend**: Lara<PERSON> 12 (PHP 8.2+)
- **Frontend**: React 18 with Vite
- **Database**: MySQL 8.0+
- **Authentication**: Laravel Sanctum (Token-based API authentication)
- **Styling**: Bootstrap 5.2.3 with custom CSS
- **File Storage**: Laravel Storage (Local/Cloud)

### 2.2 Architecture Pattern
- **API-First Design**: RESTful API backend with React SPA frontend
- **Separation of Concerns**: Clear separation between backend logic and frontend presentation
- **Modular Structure**: Component-based React architecture with Laravel resource controllers

---

## 3. Core Functional Requirements

### 3.1 User Authentication & Authorization

#### 3.1.1 User Registration & Login
- **Registration**: New user account creation with email verification
- **Login**: Secure authentication using email and password
- **Token Management**: JWT token-based session management
- **Logout**: Secure session termination

#### 3.1.2 Access Control
- Role-based access control (RBAC)
- Protected routes and API endpoints
- Session timeout management

### 3.2 Client Management Module

#### 3.2.1 Client Registration
- **Company Information**: Company name, contact person, email, phone
- **Address Management**: Complete business address information
- **Logo Upload**: Company logo storage and display
- **Status Management**: Active/Inactive client status tracking

#### 3.2.2 Client Operations
- **CRUD Operations**: Create, Read, Update, Delete client records
- **Search & Filter**: Client search by company name, status, contact info
- **Pagination**: Efficient data loading for large client lists
- **File Management**: Logo upload, update, and deletion

### 3.3 Job Title Management Module

#### 3.3.1 Job Posting Creation
- **Job Details**: Title, description, requirements, posted date
- **Client Association**: Link job postings to specific clients
- **Slot Management**: Available position count tracking
- **Status Control**: Active/Inactive job posting status

#### 3.3.2 Job Management Operations
- **CRUD Operations**: Full job posting lifecycle management
- **Search & Filter**: Job search by title, client, status
- **Requirement Tracking**: Detailed job requirement specifications

### 3.4 Applicant Management Module

#### 3.4.1 Applicant Profile Management
- **Personal Information**: 
  - Basic details: First name, middle name, last name
  - Contact info: Email, phone number, address
  - Demographics: Age, sex, civil status, date/place of birth
- **Professional Information**:
  - Educational attainment
  - Skills and competencies
  - Applied job position
- **Document Management**:
  - Resume upload and storage
  - Profile photo upload
  - Document version control

#### 3.4.2 Applicant Operations
- **Application Tracking**: Status monitoring throughout hiring process
- **Document Management**: Resume and photo upload/update/delete
- **Search & Filter**: Advanced applicant search capabilities
- **Profile Updates**: Real-time applicant information updates

### 3.5 Contract Management Module

#### 3.5.1 Employment Contract Creation
- **Contract Details**: Start date, end date, designation
- **Compensation**: Daily rate, employment status
- **Associations**: Link to applicant and job title
- **Remarks**: Additional contract notes and conditions

#### 3.5.2 Contract Operations
- **Contract Lifecycle**: From creation to termination
- **Status Tracking**: Active, expired, terminated contracts
- **Relationship Management**: Applicant-job-client relationships

### 3.6 Employee Management Module

#### 3.6.1 Active Employee Tracking
- **Employee Directory**: Current active employees
- **Contract Status**: Real-time employment status
- **Profile Integration**: Link to applicant profiles and contracts

### 3.7 Payroll Management Module

#### 3.7.1 Payroll Calculation Engine
- **Basic Salary Computation**:
  - Regular working hours and days
  - Daily rate calculations
  - Basic salary computation
- **Overtime & Holiday Pay**:
  - Regular overtime (OT)
  - Night shift differential (NSD)
  - Rest day work and overtime
  - Special holiday and legal holiday rates
  - Combined rates (holiday + night shift + rest day)

#### 3.7.2 Deductions Management
- **Government Contributions**:
  - SSS (Social Security System) employee and employer shares
  - HDMF (Pag-IBIG) employee and employer shares
  - PhilHealth employee and employer shares
  - Withholding tax calculations
- **Other Deductions**:
  - Loans (SSS, HDMF, other loans)
  - Uniform and PPE costs
  - Insurance premiums
  - Late/undertime deductions
  - Other miscellaneous deductions

#### 3.7.3 Payroll Processing
- **Cut-off Management**: Bi-monthly payroll periods
- **Gross Pay Calculation**: Total earnings before deductions
- **Net Pay Calculation**: Final take-home pay
- **13th Month Pay**: Annual bonus computation
- **Service Incentive Leave (SIL)**: Leave credits calculation

#### 3.7.4 Client Billing Module
- **Billing Calculations**:
  - Basic billing rates
  - Additional salary components
  - Administrative fees
  - VAT calculations
  - Total billing amounts
- **Government Remittances**:
  - Employer share calculations
  - Government payable amounts
  - Compliance reporting

---

## 4. Technical Requirements

### 4.1 Performance Requirements
- **Response Time**: API responses under 2 seconds
- **Concurrent Users**: Support for 50+ simultaneous users
- **File Upload**: Support files up to 10MB
- **Database**: Efficient queries with proper indexing

### 4.2 Security Requirements
- **Authentication**: Secure token-based authentication
- **Data Encryption**: Sensitive data encryption at rest
- **File Security**: Secure file upload and storage
- **Access Control**: Role-based permissions
- **HTTPS**: Secure data transmission

### 4.3 Data Requirements
- **Data Integrity**: Referential integrity constraints
- **Backup**: Regular automated backups
- **Audit Trail**: Change tracking for critical data
- **Data Retention**: Configurable data retention policies

---

## 5. User Interface Requirements

### 5.1 Design Principles
- **Responsive Design**: Mobile-friendly interface
- **Intuitive Navigation**: Clear menu structure and breadcrumbs
- **Consistent UI**: Bootstrap-based consistent styling
- **Accessibility**: WCAG compliance for accessibility

### 5.2 Key UI Components
- **Dashboard**: Overview of system metrics and quick actions
- **Data Tables**: Sortable, filterable, paginated data displays
- **Forms**: Comprehensive forms with validation
- **File Upload**: Drag-and-drop file upload interface
- **Modal Dialogs**: Confirmation dialogs and detail views

---

## 6. Integration Requirements

### 6.1 File Storage Integration
- **Local Storage**: Laravel storage for file management
- **Cloud Storage**: Optional cloud storage integration
- **File Types**: Support for PDF, DOC, DOCX, JPG, PNG files

### 6.2 Database Integration
- **MySQL**: Primary database with proper relationships
- **Migrations**: Version-controlled database schema
- **Seeders**: Sample data for development and testing

---

## 7. Compliance Requirements

### 7.1 Philippine Labor Law Compliance
- **Government Contributions**: SSS, HDMF, PhilHealth calculations
- **Tax Compliance**: Withholding tax calculations
- **Holiday Pay**: Philippine holiday pay regulations
- **Overtime Rules**: Philippine overtime compensation rules

### 7.2 Data Privacy
- **Data Protection**: Personal data protection measures
- **Consent Management**: User consent for data processing
- **Right to Access**: Data subject rights implementation

---

## 8. Quality Assurance Requirements

### 8.1 Testing Requirements
- **Unit Testing**: Backend API testing
- **Integration Testing**: Frontend-backend integration
- **User Acceptance Testing**: End-user testing scenarios
- **Performance Testing**: Load and stress testing

### 8.2 Documentation Requirements
- **API Documentation**: Comprehensive API documentation
- **User Manual**: End-user documentation
- **Technical Documentation**: System architecture and setup guides
- **Code Documentation**: Inline code documentation

---

## 9. Deployment & Maintenance

### 9.1 Deployment Requirements
- **Environment Setup**: Development, staging, production environments
- **CI/CD Pipeline**: Automated deployment pipeline
- **Server Requirements**: PHP 8.2+, MySQL 8.0+, Node.js 18+

### 9.2 Maintenance Requirements
- **Regular Updates**: Framework and dependency updates
- **Monitoring**: System performance and error monitoring
- **Backup Strategy**: Regular data backups and recovery procedures
- **Support**: User support and issue resolution

---

## 10. Success Metrics

### 10.1 Performance Metrics
- **System Uptime**: 99.9% availability
- **Response Time**: Average API response under 1 second
- **User Satisfaction**: User feedback scores above 4.0/5.0

### 10.2 Business Metrics
- **Process Efficiency**: 50% reduction in manual HR tasks
- **Data Accuracy**: 99% accuracy in payroll calculations
- **User Adoption**: 90% user adoption rate within 3 months

---

*This document serves as the comprehensive requirements specification for the EPMSI HR Management System and should be reviewed and updated as the system evolves.*
