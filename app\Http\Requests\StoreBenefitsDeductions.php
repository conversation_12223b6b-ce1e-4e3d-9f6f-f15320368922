<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBenefitsDeductionsRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'contract_id' => 'required|integer',
            'ec_ee_share' => 'required|numeric',
            'ec_er_share' => 'required|numeric',
            'ec_gov' => 'required|numeric',
            'hdmf_ee_share' => 'required|numeric',
            'hdmf_er_share' => 'required|numeric',
            'hdmf_gov' => 'required|numeric',
            'phic_ee_share' => 'required|numeric',
            'phic_er_share' => 'required|numeric',
            'phic_gov' => 'required|numeric',
            'ss_ee_share' => 'required|numeric',
            'sss_er_share' => 'required|numeric',
            'sss_gov' => 'required|numeric',
            'datecreated' => 'nullable|date',
        ];
    }
}
