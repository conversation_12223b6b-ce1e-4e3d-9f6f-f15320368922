# nice-forms.css
#### Your CSS base setup for forms and inputs

I like pretty forms and clean HTML 😄 That's why I created <strong>nice-forms.css</strong> to help devs who want to hit the ground running, but don't want to stare at default input fields when doing so 💩

Check out the [demo page](https://nielsvoogt.github.io/nice-forms.css/) for more details

### Installation

Import nice-forms.css from [unpkg](https://unpkg.com/)

```html
<link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css" />
```
Or for the reset only
```html
<link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms-reset.css" />
```

Or install via [npm](https://www.npmjs.com/package/nice-forms.css).

```
npm install nice-forms.css
```

### Contributing

If you encounter a bug on any device or have suggestions for improvement, don't hesitate to open a bug report or pull request.

### License

[MIT](https://github.com/nielsVoogt/nice-forms.css/blob/main/LICENSE)