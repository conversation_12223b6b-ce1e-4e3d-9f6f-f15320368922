<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payroll;
use App\Http\Requests\StorePayrollRequest;
use App\Http\Requests\UpdatePayrollRequest;
use App\Http\Resources\PayrollResource;
use Illuminate\Http\Request;

class PayrollController extends Controller
{
    public function index(Request $request)
    {
        // Eager load the necessary relationships
        $payrolls = Payroll::with(['contracts.applicant', 'contracts.jobTitle.client'])->paginate(10);

        // Return the paginated payroll records with the related data
        return PayrollResource::collection($payrolls);
    }
    public function store(StorePayrollRequest $request)
    {
        $payroll = Payroll::create($request->validated());
        return new PayrollResource($payroll);
    }

    public function show(Payroll $payroll)
    {
        return new PayrollResource($payroll);
    }

    public function update(UpdatePayrollRequest $request, Payroll $payroll)
    {
        $payroll->update($request->validated());
        return new PayrollResource($payroll);
    }

    public function destroy(Payroll $payroll)
    {
        $payroll->delete();
        return response()->noContent();
    }
}
