!function(t){"use strict";function n(t,n,i,e){t.beginPath(),t.arc(n,i,e,0,p,!1),t.fill()}function i(t,n,i,e,a){t.beginPath(),t.moveTo(n,i),t.lineTo(e,a),t.stroke()}function e(t,i,e,a,o,r,l,s){var h=Math.cos(i*p),c=Math.sin(i*p);s-=l,n(t,e-c*o,a+h*r+.5*s,l+(1-.5*h)*s)}function a(t,n,i,a,o,r,l,s){var h;for(h=5;h--;)e(t,n+h/5,i,a,o,r,l,s)}function o(t,n,i,e,o,r,l){n/=3e4;var s=.21*o,h=.12*o,c=.24*o,u=.28*o;t.fillStyle=l,a(t,n,i,e,s,h,c,u),t.globalCompositeOperation="destination-out",a(t,n,i,e,s,h,c-r,u-r),t.globalCompositeOperation="source-over"}function r(t,n,e,a,o,r,l){n/=12e4;var s,h,c,u,v=.25*o-.5*r,f=.32*o+.5*r,d=.5*o-.5*r;for(t.strokeStyle=l,t.lineWidth=r,t.lineCap="round",t.lineJoin="round",t.beginPath(),t.arc(e,a,v,0,p,!1),t.stroke(),s=8;s--;)h=(n+s/8)*p,c=Math.cos(h),u=Math.sin(h),i(t,e+c*f,a+u*f,e+c*d,a+u*d)}function l(t,n,i,e,a,o,r){n/=15e3;var l=.29*a-.5*o,s=.05*a,h=Math.cos(n*p),c=h*p/-16;t.strokeStyle=r,t.lineWidth=o,t.lineCap="round",t.lineJoin="round",i+=h*s,t.beginPath(),t.arc(i,e,l,c+p/8,c+7*p/8,!1),t.arc(i+Math.cos(c)*l*w,e+Math.sin(c)*l*w,l,c+5*p/8,c+3*p/8,!0),t.closePath(),t.stroke()}function s(t,n,i,e,a,o,r){n/=1350;var l,s,h,c,u=.16*a,v=11*p/12,f=7*p/12;for(t.fillStyle=r,l=4;l--;)s=(n+l/4)%1,h=i+(l-1.5)/1.5*(1===l||2===l?-1:1)*u,c=e+s*s*a,t.beginPath(),t.moveTo(h,c-1.5*o),t.arc(h,c,.75*o,v,f,!1),t.fill()}function h(t,n,e,a,o,r,l){n/=750;var s,h,c,u,v=.1875*o;for(t.strokeStyle=l,t.lineWidth=.5*r,t.lineCap="round",t.lineJoin="round",s=4;s--;)h=(n+s/4)%1,c=Math.floor(e+(s-1.5)/1.5*(1===s||2===s?-1:1)*v)+.5,u=a+h*o,i(t,c,u-1.5*r,c,u+1.5*r)}function c(t,n,e,a,o,r,l){n/=3e3;var s,h,c,u,v=.16*o,f=.75*r,d=n*p*.7,m=Math.cos(d)*f,g=Math.sin(d)*f,M=d+p/3,w=Math.cos(M)*f,C=Math.sin(M)*f,y=d+2*p/3,b=Math.cos(y)*f,k=Math.sin(y)*f;for(t.strokeStyle=l,t.lineWidth=.5*r,t.lineCap="round",t.lineJoin="round",s=4;s--;)h=(n+s/4)%1,c=e+Math.sin((h+s/4)*p)*v,u=a+h*o,i(t,c-m,u-g,c+m,u+g),i(t,c-w,u-C,c+w,u+C),i(t,c-b,u-k,c+b,u+k)}function u(t,n,i,e,o,r,l){n/=3e4;var s=.21*o,h=.06*o,c=.21*o,u=.28*o;t.fillStyle=l,a(t,n,i,e,s,h,c,u),t.globalCompositeOperation="destination-out",a(t,n,i,e,s,h,c-r,u-r),t.globalCompositeOperation="source-over"}function v(t,n,i,e,a,o,r){var l=a/8,s=l/3,h=2*s,c=n%1*p,u=Math.cos(c),v=Math.sin(c);t.fillStyle=r,t.strokeStyle=r,t.lineWidth=o,t.lineCap="round",t.lineJoin="round",t.beginPath(),t.arc(i,e,l,c,c+Math.PI,!1),t.arc(i-s*u,e-s*v,h,c+Math.PI,c,!1),t.arc(i+h*u,e+h*v,s,c+Math.PI,c,!0),t.globalCompositeOperation="destination-out",t.fill(),t.globalCompositeOperation="source-over",t.stroke()}function f(t,n,i,e,a,o,r,l,s){n/=2500;var h,c,u,f,d=C[r],m=(n+r-y[r].start)%l,g=(n+r-y[r].end)%l,M=(n+r)%l;if(t.strokeStyle=s,t.lineWidth=o,t.lineCap="round",t.lineJoin="round",1>m){if(t.beginPath(),m*=d.length/2-1,h=Math.floor(m),m-=h,h*=2,h+=2,t.moveTo(i+(d[h-2]*(1-m)+d[h]*m)*a,e+(d[h-1]*(1-m)+d[h+1]*m)*a),1>g){for(g*=d.length/2-1,c=Math.floor(g),g-=c,c*=2,c+=2,f=h;f!==c;f+=2)t.lineTo(i+d[f]*a,e+d[f+1]*a);t.lineTo(i+(d[c-2]*(1-g)+d[c]*g)*a,e+(d[c-1]*(1-g)+d[c+1]*g)*a)}else for(f=h;f!==d.length;f+=2)t.lineTo(i+d[f]*a,e+d[f+1]*a);t.stroke()}else if(1>g){for(t.beginPath(),g*=d.length/2-1,c=Math.floor(g),g-=c,c*=2,c+=2,t.moveTo(i+d[0]*a,e+d[1]*a),f=2;f!==c;f+=2)t.lineTo(i+d[f]*a,e+d[f+1]*a);t.lineTo(i+(d[c-2]*(1-g)+d[c]*g)*a,e+(d[c-1]*(1-g)+d[c+1]*g)*a),t.stroke()}1>M&&(M*=d.length/2-1,u=Math.floor(M),M-=u,u*=2,u+=2,v(t,n,i+(d[u-2]*(1-M)+d[u]*M)*a,e+(d[u-1]*(1-M)+d[u+1]*M)*a,a,o,s))}var d,m;!function(){var n=t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.mozRequestAnimationFrame||t.oRequestAnimationFrame||t.msRequestAnimationFrame,i=t.cancelAnimationFrame||t.webkitCancelAnimationFrame||t.mozCancelAnimationFrame||t.oCancelAnimationFrame||t.msCancelAnimationFrame;n&&i?(d=function(t){function i(){e.value=n(i),t()}var e={value:null};return i(),e},m=function(t){i(t.value)}):(d=setInterval,m=clearInterval)}();var g=500,M=.08,p=2*Math.PI,w=2/Math.sqrt(2),C=[[-.75,-.18,-.7219,-.1527,-.6971,-.1225,-.6739,-.091,-.6516,-.0588,-.6298,-.0262,-.6083,.0065,-.5868,.0396,-.5643,.0731,-.5372,.1041,-.5033,.1259,-.4662,.1406,-.4275,.1493,-.3881,.153,-.3487,.1526,-.3095,.1488,-.2708,.1421,-.2319,.1342,-.1943,.1217,-.16,.1025,-.129,.0785,-.1012,.0509,-.0764,.0206,-.0547,-.012,-.0378,-.0472,-.0324,-.0857,-.0389,-.1241,-.0546,-.1599,-.0814,-.1876,-.1193,-.1964,-.1582,-.1935,-.1931,-.1769,-.2157,-.1453,-.229,-.1085,-.2327,-.0697,-.224,-.0317,-.2064,.0033,-.1853,.0362,-.1613,.0672,-.135,.0961,-.1051,.1213,-.0706,.1397,-.0332,.1512,.0053,.158,.0442,.1624,.0833,.1636,.1224,.1615,.1613,.1565,.1999,.15,.2378,.1402,.2749,.1279,.3118,.1147,.3487,.1015,.3858,.0892,.4236,.0787,.4621,.0715,.5012,.0702,.5398,.0766,.5768,.089,.6123,.1055,.6466,.1244,.6805,.144,.7147,.163,.75,.18],[-.75,0,-.7033,.0195,-.6569,.0399,-.6104,.06,-.5634,.0789,-.5155,.0954,-.4667,.1089,-.4174,.1206,-.3676,.1299,-.3174,.1365,-.2669,.1398,-.2162,.1391,-.1658,.1347,-.1157,.1271,-.0661,.1169,-.017,.1046,.0316,.0903,.0791,.0728,.1259,.0534,.1723,.0331,.2188,.0129,.2656,-.0064,.3122,-.0263,.3586,-.0466,.4052,-.0665,.4525,-.0847,.5007,-.1002,.5497,-.113,.5991,-.124,.6491,-.1325,.6994,-.138,.75,-.14]],y=[{start:.36,end:.11},{start:.56,end:.16}],b=function(t){this.list=[],this.interval=null,this.color=t&&t.color?t.color:"black",this.resizeClear=!(!t||!t.resizeClear)};b.CLEAR_DAY=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,o=Math.min(e,a);r(t,n,.5*e,.5*a,o,o*M,i)},b.CLEAR_NIGHT=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,o=Math.min(e,a);l(t,n,.5*e,.5*a,o,o*M,i)},b.PARTLY_CLOUDY_DAY=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,l=Math.min(e,a);r(t,n,.625*e,.375*a,.75*l,l*M,i),o(t,n,.375*e,.625*a,.75*l,l*M,i)},b.PARTLY_CLOUDY_NIGHT=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,r=Math.min(e,a);l(t,n,.667*e,.375*a,.75*r,r*M,i),o(t,n,.375*e,.625*a,.75*r,r*M,i)},b.CLOUDY=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,r=Math.min(e,a);o(t,n,.5*e,.5*a,r,r*M,i)},b.RAIN=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,r=Math.min(e,a);s(t,n,.5*e,.37*a,.9*r,r*M,i),o(t,n,.5*e,.37*a,.9*r,r*M,i)},b.SLEET=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,r=Math.min(e,a);h(t,n,.5*e,.37*a,.9*r,r*M,i),o(t,n,.5*e,.37*a,.9*r,r*M,i)},b.SNOW=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,r=Math.min(e,a);c(t,n,.5*e,.37*a,.9*r,r*M,i),o(t,n,.5*e,.37*a,.9*r,r*M,i)},b.WIND=function(t,n,i){var e=t.canvas.width,a=t.canvas.height,o=Math.min(e,a);f(t,n,.5*e,.5*a,o,o*M,0,2,i),f(t,n,.5*e,.5*a,o,o*M,1,2,i)},b.FOG=function(t,n,e){var a=t.canvas.width,o=t.canvas.height,r=Math.min(a,o),l=r*M;u(t,n,.5*a,.32*o,.75*r,l,e),n/=5e3;var s=Math.cos(n*p)*r*.02,h=Math.cos((n+.25)*p)*r*.02,c=Math.cos((n+.5)*p)*r*.02,v=Math.cos((n+.75)*p)*r*.02,f=.936*o,d=Math.floor(f-.5*l)+.5,m=Math.floor(f-2.5*l)+.5;t.strokeStyle=e,t.lineWidth=l,t.lineCap="round",t.lineJoin="round",i(t,s+.2*a+.5*l,d,h+.8*a-.5*l,d),i(t,c+.2*a+.5*l,m,v+.8*a-.5*l,m)},b.prototype={_determineDrawingFunction:function(t){return"string"==typeof t?(t=t.toUpperCase().replace(/-/g,"_"),b.hasOwnProperty(t)?b[t]:null):void 0},add:function(t,n){var i;"string"==typeof t&&(t=document.getElementById(t)),null!==t&&(n=this._determineDrawingFunction(n),"function"==typeof n&&(i={element:t,context:t.getContext("2d"),drawing:n},this.list.push(i),this.draw(i,g)))},set:function(t,n){var i;for("string"==typeof t&&(t=document.getElementById(t)),i=this.list.length;i--;)if(this.list[i].element===t)return this.list[i].drawing=this._determineDrawingFunction(n),void this.draw(this.list[i],g);this.add(t,n)},remove:function(t){var n;for("string"==typeof t&&(t=document.getElementById(t)),n=this.list.length;n--;)if(this.list[n].element===t)return void this.list.splice(n,1)},draw:function(t,n){var i=t.context.canvas;this.resizeClear?i.width=i.width:t.context.clearRect(0,0,i.width,i.height),t.drawing(t.context,n,this.color)},play:function(){var t=this;this.pause(),this.interval=d(function(){var n,i=Date.now();for(n=t.list.length;n--;)t.draw(t.list[n],i)},1e3/60)},pause:function(){this.interval&&(m(this.interval),this.interval=null)}},t.Skycons=b}(this);