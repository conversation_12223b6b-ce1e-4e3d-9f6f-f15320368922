<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contract extends Model
{
    use HasFactory;
    protected $fillable = [
        'applicant_id', 'jobtitle_id', 'start_date', 'end_date', 'designation',
        'daily_rate', 'emp_status', 'remarks',
    ];

    public function applicant()
    {
        return $this->belongsTo(Applicant::class, 'applicant');
    }

    public function jobTitle()
    {
        return $this->belongsTo(JobTitle::class, 'jobtitle_id');
    }
}
