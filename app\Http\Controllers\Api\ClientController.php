<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Http\Requests\StoreClientRequest;
use App\Http\Requests\UpdateClientRequest;
use App\Http\Resources\ClientResource;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;

class ClientController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreClientRequest $request)
    {
        $clientData = $request->except('logo'); // Get data excluding logo

        $client = Client::create($clientData);

        if ($request->hasFile('logo')) {
            $file = $request->file('logo');
            $filename = $client->id . '.' . $file->getClientOriginalExtension();
            $file->storeAs('public/logos', $filename);
            $client->update(['logo' => $filename]);
        }

        return response(new ClientResource($client), 201);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateClientRequest $request, Client $client)
    {
        $clientData = $request->except('logo'); // Get data excluding logo

        $client->update($clientData);

        if ($request->hasFile('logo')) {
            // Delete old logo file if it exists
            if ($client->logo && Storage::exists('public/logos/' . $client->logo)) {
                Storage::delete('public/logos/' . $client->logo);
            }

            $file = $request->file('logo');
            $filename = $client->id . '.' . $file->getClientOriginalExtension();
            $file->storeAs('public/logos', $filename);
            $client->update(['logo' => $filename]);
        }

        return new ClientResource($client);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return ClientResource::collection(Client::query()->orderBy('company_name')->paginate(10));
    }

    /**
     * Display the specified resource.
     */
    public function show(Client $client)
    {
        return new ClientResource($client);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Client $client)
    {
        if ($client->logo && Storage::exists('public/logos/' . $client->logo)) {
            Storage::delete('public/logos/' . $client->logo);
        }
        $client->delete();

        return response()->noContent();
    }
}
