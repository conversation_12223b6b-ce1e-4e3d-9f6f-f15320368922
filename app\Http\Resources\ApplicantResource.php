<?php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ApplicantResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'last_name' => $this->last_name,
            'first_name' => $this->first_name,
            'middle_name' => $this->middle_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'resume' => $this->resume ? asset('storage/resumes/' . $this->resume) : null,
            'skills' => $this->skills,
            'highest_educ_attainment' => $this->highest_educ_attainment,
            'sex' => $this->sex,
            'age' => $this->age,
            'status' => $this->status,
            'image' => $this->image ? asset('storage/images/' . $this->image) : null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
