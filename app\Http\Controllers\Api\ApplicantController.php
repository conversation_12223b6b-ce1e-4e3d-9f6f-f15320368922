<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Applicant;
use Illuminate\Http\Request;
use App\Http\Resources\ApplicantResource;
use App\Http\Requests\StoreApplicantRequest;
use App\Http\Requests\UpdateApplicantRequest;
use Illuminate\Support\Facades\Storage;

class ApplicantController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return ApplicantResource::collection(Applicant::query()
            // ->where('status', 'Active') // Filter for active applicants
            ->orderBy('last_name', 'asc') // Order by last name
            ->paginate(10)); // Paginate results
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreApplicantRequest $request)
    {
        $applicantData = $request->except('resume', 'image'); // Get data excluding resume and image

        $applicant = Applicant::create($applicantData);

        if ($request->hasFile('resume')) {
            $file = $request->file('resume');
            $resumeFilename = $applicant->id . '.' . $file->getClientOriginalExtension();
            $file->storeAs('public/resumes', $resumeFilename);
            $applicant->update(['resume' => $resumeFilename]);
        }

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $imageFilename = $applicant->id . '.' . $file->getClientOriginalExtension();
            $file->storeAs('public/images', $imageFilename);
            $applicant->update(['image' => $imageFilename]);
        }

        return response(new ApplicantResource($applicant), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Applicant $applicant)
    {
        return new ApplicantResource($applicant);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateApplicantRequest $request, Applicant $applicant)
    {
        $applicantData = $request->except('resume', 'image'); // Get data excluding resume and image

        $applicant->update($applicantData);

        if ($request->hasFile('resume')) {
            // Delete old resume file if it exists
            if ($applicant->resume && Storage::exists('public/resumes/' . $applicant->resume)) {
                Storage::delete('public/resumes/' . $applicant->resume);
            }

            $file = $request->file('resume');
            $resumeFilename = $applicant->id . '.' . $file->getClientOriginalExtension();
            $file->storeAs('public/resumes', $resumeFilename);
            $applicant->update(['resume' => $resumeFilename]);
        }

        if ($request->hasFile('image')) {
            // Delete old image file if it exists
            if ($applicant->image && Storage::exists('public/images/' . $applicant->image)) {
                Storage::delete('public/images/' . $applicant->image);
            }

            $file = $request->file('image');
            $imageFilename = $applicant->id . '.' . $file->getClientOriginalExtension();
            $file->storeAs('public/images', $imageFilename);
            $applicant->update(['image' => $imageFilename]);
        }

        return new ApplicantResource($applicant);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Applicant $applicant)
    {
        if ($applicant->resume && Storage::exists('public/resumes/' . $applicant->resume)) {
            Storage::delete('public/resumes/' . $applicant->resume);
        }

        if ($applicant->image && Storage::exists('public/images/' . $applicant->image)) {
            Storage::delete('public/images/' . $applicant->image);
        }

        $applicant->delete();

        return response()->noContent();
    }
}
