<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payroll extends Model
{
    use HasFactory;

    protected $fillable = [
        'contract_id',
        'cut_off_date',
        'rw_hrs',
        'rw_day',
        'daily_rate',
        'basic_salary',
        'thirteenth_month_pay',
        'sil',
        'gross_pay',
        'total_deductions',
        'net_pay',
        'datecreated',
        'dateupdated',
    ];

    public function contracts()
    {
        return $this->belongsTo(Contract::class);
    }
}
