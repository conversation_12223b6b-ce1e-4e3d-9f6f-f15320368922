<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBenefitsDeductionsTable extends Migration
{
    public function up()
    {
        Schema::create('benefits_deductions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contract_id')->constrained()->onDelete('cascade');
            $table->decimal('ec_ee_share', 10, 4);
            $table->decimal('ec_er_share', 10, 4);
            $table->decimal('ec_gov', 10, 4);
            $table->decimal('hdmf_ee_share', 10, 4);
            $table->decimal('hdmf_er_share', 10, 4);
            $table->decimal('hdmf_gov', 10, 4);
            $table->decimal('phic_ee_share', 10, 4);
            $table->decimal('phic_er_share', 10, 4);
            $table->decimal('phic_gov', 10, 4);
            $table->decimal('ss_ee_share', 10, 4);
            $table->decimal('sss_er_share', 10, 4);
            $table->decimal('sss_gov', 10, 4);
            $table->timestamp('datecreated')->useCurrent();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('benefits_deductions');
    }
}
