<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('payroll_deductions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payroll_id')->constrained('payroll')->onDelete('cascade')->onUpdate('cascade');
            $table->decimal('hrs_late_ut', 10, 4)->default(0.0000);
            $table->decimal('amt_late_ut', 10, 4)->default(0.0000);
            $table->decimal('sss_loan', 10, 4)->default(0.0000);
            $table->decimal('hdmf_loan', 10, 4)->default(0.0000);
            $table->decimal('uniform_ppe', 10, 4)->default(0.0000);
            $table->decimal('insurance', 10, 4)->default(0.0000);
            $table->decimal('other_loans', 10, 4)->default(0.0000);
            $table->decimal('other_deduc', 10, 4)->default(0.0000);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payroll_deductions');
    }
};
