<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePayrollBillingRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'payroll_id' => 'required|exists:payroll,id',
            'insurance' => 'required|numeric',
            'uniform_ppe' => 'required|numeric',
            'late_ut' => 'required|numeric',
            'gross' => 'required|numeric',
            'net_pay' => 'required|numeric',
            'basic_pay' => 'required|numeric',
            'payable_ee' => 'required|numeric',
            'payable_gov' => 'required|numeric',
            'sub_billing' => 'required|numeric',
            'admin_fee' => 'required|numeric',
            'admin_subtotal' => 'required|numeric',
            'gross_pay_basic' => 'required|numeric',
            'add_salaries' => 'required|numeric',
            'basic_billing' => 'required|numeric',
            'vat' => 'required|numeric',
            'total_billing' => 'required|numeric',
        ];
    }
}
