import { Link, Navigate, Outlet } from "react-router-dom";
import { useStateContext } from "../contexts/ContextProvider";
import axiosClient from "../axios-client.js";
import { useEffect } from "react";
import "bootstrap/dist/js/bootstrap.bundle.min"; // Ensure Bootstrap JS is imported

export default function DefaultLayout() {
  const { user, token, setUser, setToken, notification } = useStateContext();

  if (!token) {
    return <Navigate to="/login" />;
  }

  const onLogout = (ev) => {
    ev.preventDefault();

    axiosClient.post("/logout").then(() => {
      setUser({});
      setToken(null);
    });
  };

  useEffect(() => {
    axiosClient.get("/user").then(({ data }) => {
      setUser(data);
    });
  }, []);

  const renderAccordionItem = (id, label, submenu) => (
    <div className="accordion-item" key={id}>
      <h2 className="accordion-header" id={`heading-${id}`}>
        <button
          className="accordion-button collapsed"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target={`#collapse-${id}`}
          aria-expanded="false"
          aria-controls={`collapse-${id}`}
        >
          {label}
        </button>
      </h2>
      <div
        id={`collapse-${id}`}
        className="accordion-collapse collapse"
        aria-labelledby={`heading-${id}`}
        data-bs-parent="#sidebarAccordion"
      >
        <div className="accordion-body">
          <ul className="nav flex-column">
            {submenu.map((subItem, index) => (
              <li key={index} className="nav-item">
                <Link to={subItem.to} className="nav-link">
                  {subItem.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );

  const menuItems = [
    {
      label: "Clients",
      submenu: [
        { label: "Add Client", to: "/clients/new" },
        { label: "Manage Clients", to: "/clients" },
      ],
    },
    {
      label: "Jobs",
      submenu: [
        { label: "Add Job", to: "/jobtitles/new" },
        { label: "Manage Jobs", to: "/jobtitle" },
      ],
    },
    {
      label: "Applications",
      submenu: [
        { label: "Add Applicant", to: "/applicants/new" },
        { label: "Manage Applications", to: "/applicants" },
      ],
    },
    {
      label: "Employees",
      submenu: [{ label: "Manage Employees", to: "/manageemployees" }],
    },
    {
      label: "Contracts",
      submenu: [
        { label: "Add Contracts", to: "/contracts/new" },
        { label: "Manage Contracts", to: "/contracts" },
      ],
    },
    {
      label: "Payroll",
      submenu: [
        { label: "Generate Payroll", to: "/payrolls/new" },
        { label: "View Payroll", to: "/payrolls" },
      ],
    },
    {
      label: "Invoice",
      submenu: [
        { label: "Create Invoice", to: "/create_invoice" },
        { label: "Manage Invoice", to: "/manage_invoice" },
      ],
    },
    {
      label: "Reports",
      submenu: [
        { label: "Clients", to: "/employee_report" },
        { label: "Contracts", to: "/client_report" },
        { label: "Billings", to: "/manage_billings" },
      ],
    },
    {
      label: "Task Scheduler",
      submenu: [{ label: "Manage Tasks", to: "/view_task" }],
    },
  ];
  return (
    <div className="container-fluid">
      <div className="row">
        <nav className="col-md-3 col-lg-2 d-md-block bg-light sidebar">
          <div className="sidebar-content">
            <div className="sidebar-header">
              <Link to="/dashboard" className="text-center d-block mb-3">
                <img
                  src="./public/logo1.png"
                  alt="Company Logo"
                  className="rounded-circle logo-img"
                />
              </Link>
              <h1 className="h4 text-center mb-4">
                EASTERN PRIDE MANPOWER SERVICES, INC.
              </h1>
              <div className="accordion" id="userAccordion">
                <div className="accordion-item">
                  <h2 className="accordion-header" id="userHeader">
                    <button
                      className="accordion-button"
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target="#userCollapse"
                      aria-expanded="true"
                      aria-controls="userCollapse"
                    >
                      Current User: {user.name}
                    </button>
                  </h2>
                  <div
                    id="userCollapse"
                    className="accordion-collapse collapse show"
                    aria-labelledby="userHeader"
                  >
                    <div className="accordion-body">
                      <ul className="nav flex-column">
                        <li className="nav-item">
                          <Link to="/dashboard" className="nav-link">
                            Dashboard
                          </Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/manage-profile" className="nav-link">
                            Manage Profile
                          </Link>
                        </li>
                        <li className="nav-item">
                          <Link to="/theme" className="nav-link">
                            Theme
                          </Link>
                        </li>
                        <li className="nav-item">
                          <a href="#" onClick={onLogout} className="nav-link">
                            Logout
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="accordion-scrollable">
              <div className="accordion mb-4" id="sidebarAccordion">
                {menuItems.map((item, index) =>
                  item.submenu ? (
                    renderAccordionItem(index, item.label, item.submenu)
                  ) : (
                    <li className="nav-item" key={index}>
                      <Link to={item.to} className="nav-link">
                        {item.label}
                      </Link>
                    </li>
                  )
                )}
              </div>
            </div>
          </div>
        </nav>

        <main className="col-md-9 ms-sm-auto col-lg-10 px-md-4">
          <div className="content">
            <Outlet />
          </div>
          {notification && (
            <div className="alert alert-success mt-4" role="alert">
              {notification}
            </div>
          )}
        </main>
        <footer className="sidebar-footer">
          <p className="text-center mb-0">
            © {new Date().getFullYear()} Eastern Pride Manpower Services, Inc.
          </p>
        </footer>
      </div>
    </div>
  );
}
