<?php


namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePayrollRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'contract_id' => 'sometimes|required|exists:contracts,id',
            'cut_off_date' => 'sometimes|required|date',
            'rw_hrs' => 'sometimes|required|numeric',
            'rw_day' => 'sometimes|required|numeric',
            'daily_rate' => 'sometimes|required|numeric',
            'basic_salary' => 'sometimes|required|numeric',
            'thirteenth_month_pay' => 'sometimes|required|numeric',
            'sil' => 'sometimes|required|numeric',
            'gross_pay' => 'sometimes|required|numeric',
            'total_deductions' => 'sometimes|required|numeric',
            'net_pay' => 'sometimes|required|numeric',
            'datecreated' => 'sometimes|required|date',
            'dateupdated' => 'sometimes|required|date',
        ];
    }
}
