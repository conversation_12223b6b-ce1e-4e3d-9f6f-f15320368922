{"name": "react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port=3000", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.6.0", "bootstrap": "^5.2.3", "bootstrap-icons": "^1.11.3", "nice-forms.css": "^0.1.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.1"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "vite": "^5.2.0"}}