<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PayrollBillingResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'payroll_id' => $this->payroll_id,
            'insurance' => $this->insurance,
            'uniform_ppe' => $this->uniform_ppe,
            'late_ut' => $this->late_ut,
            'gross' => $this->gross,
            'net_pay' => $this->net_pay,
            'basic_pay' => $this->basic_pay,
            'payable_ee' => $this->payable_ee,
            'payable_gov' => $this->payable_gov,
            'sub_billing' => $this->sub_billing,
            'admin_fee' => $this->admin_fee,
            'admin_subtotal' => $this->admin_subtotal,
            'gross_pay_basic' => $this->gross_pay_basic,
            'add_salaries' => $this->add_salaries,
            'basic_billing' => $this->basic_billing,
            'vat' => $this->vat,
            'total_billing' => $this->total_billing,
            'datecreated' => $this->datecreated,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

