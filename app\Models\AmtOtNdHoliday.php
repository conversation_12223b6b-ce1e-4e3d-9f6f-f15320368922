<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AmtOtNdHoliday extends Model
{
    use HasFactory;

    protected $fillable = [
        'payroll_id', 'b_ot', 'nsd', 'nsd_ot', 'rdd', 'rdd_ot', 'rdnsd', 'rdnsd_ot', 
        'sh', 'sh_ot', 'shnsd', 'shnsd_ot', 'lh', 'lh_ot', 'lhnsd', 'lhnsd_ot', 
        'lhrd', 'lhrd_ot', 'lhrdnsd', 'lhrdnsd_ot', 'shrd', 'shrd_ot', 'shrdnsd', 
        'shrdnsd_ot'
    ];

    public function payroll()
    {
        return $this->belongsTo(Payroll::class);
    }
}
